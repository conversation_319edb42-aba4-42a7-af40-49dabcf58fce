
@font-face {
  font-family:"Salsa";
  font-weight:500;
  font-style:italic;
  font-display:swap;
  src:url(https://aachips.co/wp-content/uploads/Salsa-Regular.ttf) format("truetype");
}

@font-face {
  font-family: "<PERSON>";
  src: url('../assets/fonts/Virgil.woff2');
  font-display: swap;
}

* {
  box-sizing: border-box;
}

html {
  font: 17px/1.5 'Trebuchet MS', Helvetica, sans-serif;
}

:root {
  --green: #149954; 
  --red: #E4312b;
  --black: #000000;
  --white: #fff;

  --error-red: #FF3333;
  --warning-orange: #FF9900;
  --success-green: #2ECC40;
  --info-blue: #0077CC;
  --highlight-yellow: #FFCC00;
  --background-gray: #F5F5F5; 

  --box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

body {
  background: url(../assets/elements/SoftE2.png) no-repeat fixed center/cover;
  cursor: url(../assets/hamsa24.svg), auto;
}

#wrapper {
  background-color: var(--background-gray);
  margin: 1rem auto;
  padding-bottom: 2rem;
  width: 90%;
  border: 1px solid #333;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

main {
  padding: 1rem;
}

h2, h3, h4 {
  font-size: 1.5rem;
  font-family: 'Virgil', sans-serif;
}

img {
  max-width: 100%;
  height: auto;
}

/* Header */

header {
  background-color: var(--background-gray);
  max-width: 90%;
  margin: 0 auto;
  border: 1px solid #333;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

header h1 {
  font-family: "Virgil", sans-serif;
  font-size: 3rem;
}

header div {
  display: flex;
  justify-content: space-around;
}

/* Navigation Bar */

nav {
  background-color: #333;
  border-radius: 5px;
  display: flex;
  justify-content: space-around;
  margin: 0 auto;
  overflow: hidden;
  width: 100%;
}

nav ul {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

nav ul li {
  list-style-type: none;
}

nav a {
  color: var(--white);
  float: left;
  font-family: 'Virgil', sans-serif;
  padding: 14px 16px;
  text-align: center;
  text-decoration: none;
}

/* nav #nav-box,
nav #nav-trigger {
  display: none;
} */
  
  /* Dropdown menu */
.dropdown {
  float: left;
  overflow: hidden;
}

.dropdown .dropbtn {
  background-color: inherit;
  border: none;
  color: white;
  margin: 0; 
  outline: none;
  padding: 14px 16px;
  font-size: 16px;
  font-family: inherit; 
}

.navbar a:hover, .dropdown:hover .dropbtn {
  background-color: red;
}

.dropdown-content {
  background-color: var(--background-gray);
  display: none;
  position: absolute;
  min-width: 160px;
  box-shadow: var(--box-shadow);
  z-index: 1;
}

.dropdown-content a {
  color: var(--black);
  display: block;
  float: none;
  padding: 12px 16px;
  text-decoration: none;
  text-align: left;
}

.dropdown-content a:hover {
  background-color: var(--background-gray);
}

.dropdown:hover .dropdown-content {
  display: block;
}

/* Footer */
footer {
  background-color: var(--background-gray);
  color: var(--black);
  padding: 2rem 0;
  margin-top: 1rem;
}

.footer-container {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
}

footer a {
  color: var(--black);
}

footer ul {
  list-style: none;
}
.column {
  text-align: center;
  width: 33.33%;
}

.column h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

/* sections */


/* Form */
form {
  margin: 0 auto;
  max-width: 500px;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  font-weight: bold;
  margin-bottom: 0.5rem;
}



#nav-trigger {
  color: #fff;
  cursor: pointer;
  display: none;
  font-size: 1.4rem;
  text-align: center;
}

#nav-box {
  display: none;
}

#nav-box:checked + nav ul li {
  display: block; 
  color: #fff;
  flex-direction: column;
}

@media screen and (max-width: 900px) {
  nav {
    align-items: center;
  }  
  #nav-box {
        display: none; 
    }
    
    #nav-trigger {
        display: block;
    }
    
    nav ul {
        display: block;
    }

    #nav-box:checked + nav ul {
        display: flex;
        flex-direction: column;
    }
}

/* nav label {
    display: block;
}
  header {
      justify-content: space-around;
  }
  header nav {
      flex-basis: 85%;
      flex-direction: column;
      flex-wrap: wrap;
  }
  header nav li {
      display: none;
  } */

@media screen and (max-width: 700px) {
  footer,
  footer ul {
      align-content: center;
      flex-direction: column;
      font-size: 1.2rem;
      padding: 1rem 0;
      text-align: center;
  }
  #social {
      flex-direction: row;
  }
}
