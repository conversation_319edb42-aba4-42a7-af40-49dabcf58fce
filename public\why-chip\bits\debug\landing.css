:root {
  --red: #C33748;
  --green: #1C5D3B;
  --black: #333;
  --white: #fff;

  --error-red: #FF3333;
  --warning-orange: #FF9900;
  --success-green: #2ECC40;
  --info-blue: #0077CC;
  --highlight-yellow: #FFCC00;
  --background-gray: #F5F5F5;
}

* {
  box-sizing: border-box;
}

html {
  font: 17px/1.5 'Trebuchet MS', Helvetica, sans-serif;
}

body {
  margin: 0;
  perspective: 1px;
  width: 100%;
}
  
#wrapper {
  margin: 0 auto;
  width: 100%;
}

button {
  padding: 1rem;
  font-size: 1.2rem;
  border-radius: 25%;
  margin: 0 2rem;
  background-color: var(--red);
  color: var(--white);
}

button:hover {
  background-color: var(--green);
  transform: scale(1.1);
  transition: transform 0.3s ease, background-color 0.3s ease;
}

img {
  max-width: 100%;
  height: auto;
}

h1 {
  text-align: center;
}
/* Form */

.form-container {
  width: 80%;
}

form {
  align-items: center;
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: var(--red);
  display: flex;
  flex-direction: column;
  margin: 0 auto 1rem;
  padding: 20px;
}

label {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

input,
textarea {
  padding: 10px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  width: 100%;
}

input[type="submit"] {
  background-color: var(--red);
  color: var(--white);
  font-size: 18px;
  font-weight: bold;
  border: none;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

form input[type="submit"]:hover {
  background-color: var(--green);
}

/* Sections */

section:nth-child(1) {
  background: var(--red) url(../assets/elements/Apple-Pile.png) no-repeat center/cover;
  color: var(--white);
  display: flex;
  flex-direction: column;
}

#fact-container {
  align-items: center;
  background-color: #333333e0;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  margin: 1rem auto;
  width: 90%;
}

#fact-container div {
  align-items: center;
  display: flex;
  gap: 1rem;
}

#fact-container a {
  color: var(--white);
  text-decoration: none;
}

#fact-container .row {
  width: 90%;
  align-items: center;
  justify-content: space-around;


}

section:nth-child(2) {
  background-image: url(../assets/chips/Chips-Pile.png);
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 500px;
  color: var(--white);
  text-align: center;
  gap: 1rem;
}

#chips h2, 
#whychip h2 {
  font-size:2rem;
}

section:nth-child(3) {
  background-color: var(--green);
  display: flex;
  align-items: center;
  color: var(--white);
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
}

section:nth-child(4) {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 1rem;
}

#reasons {
  justify-content: center;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
}

#reasons div {
  background-color: var(--white);
  border: 1px groove #333;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  padding: 1rem;
  flex-basis: 400px;
}
#reasons div:hover {
  transform: translateY(-2px);
}

section:nth-child(5) {
  align-items: center;
  background: var(--red) url(../assets/elements/Apple-Pile.png) no-repeat center/cover;
  background-color: var(--red);
  color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

#testis {
  align-items: center;
  display: flex;
  gap: 2rem;
  margin: 50px 0;
  padding: 2rem;
  background-color: var(--background-gray);
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#testis div {
  background-color: #ffffff;
  color: var(--black);
  border-radius: 15px;
  margin-bottom: 20px;
  max-width: 700px;
  padding: 30px;
  text-align: center;
  width: 90%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#testis div:hover {
  transform: translateY(-10px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

#testis p {
  margin: 0;
  font-size: 20px;
  line-height: 1.6;
}


section:nth-child(6) {
  background-color: #333;
  color: #fff;
  padding: 1rem;
}

section:nth-child(7) {
  background-color: #1C5D3B;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

#steps {
  color: var(--black);
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 1rem;
}

#steps div {
  background-color: #f4f4f4;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-basis: 300px;
  margin: 1rem;
  padding: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#steps div:hover {
  transform: translateY(-10px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

#support {
  background-color: #c33748;
}

#heartwarmers {
  background-color: var(--black);
  color: var(--white);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

#heartwarmers p {
  margin: 0;
  font-size: 20px;
  line-height: 1.6;
}

#heartwarmers img {
  margin: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#heartwarmers img:hover {
  transform: translateY(-10px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

#order {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: #1C5D3B;
  color: #fff;
}

#order h2 {
  margin-top: 0;
}

#order p {
  margin-bottom: 2rem;
}

#support {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: var(--red);
  color: var(--white);
}

@media screen and (max-width: 800px) {
  section:nth-child(3) {
    flex-direction: column;
    text-align: center;
  }

  #reasons {
    flex-direction: column;
    text-align: center;
  }

  #testis {
    flex-direction: column;
    width: 90%;

  }

  #fact-container div {
    flex-direction: column;
    margin: 0;
    text-align: center;
  }
}

.col {
  flex-direction: column;
}

.row {
  flex-direction: row;
}

