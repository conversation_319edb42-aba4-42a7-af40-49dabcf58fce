CREATE TABLE `apples_in` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `variety` VARCHAR(255) NOT NULL,
  `qty` INT NOT NULL,
  `purchase_date` DATE NOT NULL,
  `vendor_id` INT NOT NULL,
  `cost` DECIMAL(10,2) NOT NULL,
  `PO_number` VARCHAR(255),
  `notes` TEXT
);

CREATE TABLE `vendors` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL
);

CREATE TABLE `batches` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `batch_number` VARCHAR(255) NOT NULL,
  `size` INT NOT NULL,
  `baked_by` INT NOT NULL,
  `bake_date` DATE NOT NULL,
  `starting_weight` INT NOT NULL,
  `ending_weight` INT NOT NULL,
  `waste` INT NOT NULL,
  `profit` DECIMAL(10,2) NOT NULL,
  `apples_used` INT NOT NULL,
  `bags_produced` INT NOT NULL,
  `bags_donated` INT NOT NULL,
  `bags_paid` INT NOT NULL,
  `foc` BOOLEAN NOT NULL DEFAULT false,
  `charity` BOOLEAN NOT NULL DEFAULT false,
  `charitable_donation` DECIMAL(10,2) NOT NULL,
  `apples_in_id` INT NOT NULL
);

CREATE TABLE `given_away` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `batch_id` INT NOT NULL,
  `recipient_id` INT NOT NULL,
  `recipients_importance` INT NOT NULL,
  `date_given` DATE NOT NULL,
  `location` VARCHAR(255) NOT NULL,
  `reason` VARCHAR(255)
);

CREATE TABLE `sold_bags` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `batch_id` INT NOT NULL,
  `sale_date` DATE NOT NULL,
  `location` VARCHAR(255) NOT NULL,
  `units_sold` INT NOT NULL,
  `earnings` DECIMAL(10,2) NOT NULL
);

CREATE TABLE `testimonials` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `location` VARCHAR(255),
  `testimony` TEXT NOT NULL,
  `date_added` DATE NOT NULL,
  `subscriber_id` INT NOT NULL
);

CREATE TABLE `blog_posts` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT NOT NULL,
  `publish_date` DATE NOT NULL,
  `author_id` INT NOT NULL,
  `source` VARCHAR(255),
  `keyword_id` INT,
  `popularity` INT NOT NULL,
  `featured_media` VARCHAR(255),
  `qr_code` TEXT,
  `category` VARCHAR(255)
);

CREATE TABLE `categories` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `description` TEXT
);

CREATE TABLE `subscribers` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `first_name` VARCHAR(255) NOT NULL,
  `last_name` VARCHAR(255) NOT NULL,
  `email` VARCHAR(255) NOT NULL,
  `subscription_date` DATE NOT NULL,
  `subscription_importance` INT NOT NULL,
  `occupation` VARCHAR(255),
  `company` VARCHAR(255),
  `biography` TEXT
);

CREATE TABLE `co_writers` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `first_name` VARCHAR(255) NOT NULL,
  `last_name` VARCHAR(255) NOT NULL,
  `email` VARCHAR(255) NOT NULL,
  `password` VARCHAR(255) NOT NULL,
  `bio` TEXT,
  `join_date` DATE NOT NULL
);

CREATE TABLE `affiliates_and_sponsors` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `organization_name` VARCHAR(255) NOT NULL,
  `contact_name` VARCHAR(255) NOT NULL,
  `blog_post_id` INT,
  `description` TEXT NOT NULL,
  `value` DECIMAL(10,2) NOT NULL,
  `start_date` DATE NOT NULL,
  `end_date` DATE NOT NULL
);

CREATE TABLE `keywords` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `keyword` VARCHAR(255) NOT NULL
);

CREATE TABLE `workshops` (
  `id` INT PRIMARY KEY AUTO_INCREMENT,
  `type` ENUM ('school', 'church', 'non-profit') NOT NULL,
  `host_id` INT NOT NULL,
  `workshop_title` VARCHAR(255) NOT NULL,
  `format` ENUM ('virtual', 'in-person') NOT NULL,
  `duration` FLOAT NOT NULL,
  `compensation` DECIMAL(10,2) NOT NULL,
  `location` VARCHAR(255) NOT NULL,
  `start_time` TIMESTAMP NOT NULL,
  `end_time` TIMESTAMP NOT NULL,
  `description` TEXT,
  `attendees` INT NOT NULL,
  `participant_list` TEXT
);

ALTER TABLE `apples_in` ADD FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE;

ALTER TABLE `batches` ADD FOREIGN KEY (`baked_by`) REFERENCES `subscribers` (`id`) ON DELETE CASCADE;

ALTER TABLE `batches` ADD FOREIGN KEY (`apples_in_id`) REFERENCES `apples_in` (`id`) ON DELETE CASCADE;

ALTER TABLE `given_away` ADD FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE;

ALTER TABLE `given_away` ADD FOREIGN KEY (`recipient_id`) REFERENCES `subscribers` (`id`) ON DELETE CASCADE;

ALTER TABLE `sold_bags` ADD FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`) ON DELETE CASCADE;

ALTER TABLE `testimonials` ADD FOREIGN KEY (`subscriber_id`) REFERENCES `subscribers` (`id`) ON DELETE CASCADE;

ALTER TABLE `blog_posts` ADD FOREIGN KEY (`author_id`) REFERENCES `subscribers` (`id`) ON DELETE CASCADE;

ALTER TABLE `blog_posts` ADD FOREIGN KEY (`keyword_id`) REFERENCES `keywords` (`id`) ON DELETE SET NULL;

ALTER TABLE `co_writers` ADD FOREIGN KEY (`id`) REFERENCES `subscribers` (`id`) ON DELETE CASCADE;

ALTER TABLE `affiliates_and_sponsors` ADD FOREIGN KEY (`blog_post_id`) REFERENCES `blog_posts` (`id`) ON DELETE SET NULL;

ALTER TABLE `workshops` ADD FOREIGN KEY (`host_id`) REFERENCES `subscribers` (`id`) ON DELETE CASCADE;

ALTER TABLE `blog_posts` ADD FOREIGN KEY (`category`) REFERENCES `categories` (`name`);
