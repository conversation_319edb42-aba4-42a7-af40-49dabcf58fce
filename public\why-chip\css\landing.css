:root {
  --red: #C33748;
  --green: #1C5D3B;
  --black: #333;
  --white: #fff;

  --error-red: #FF3333;
  --warning-orange: #FF9900;
  --success-green: #2ECC40;
  --info-blue: #0077CC;
  --highlight-yellow: #FFCC00;
  --background-gray: #F5F5F5;
}

* {
  box-sizing: border-box;
}

html {
  font: 17px/1.5 'Trebuchet MS', Helvetica, sans-serif;
}

body {
  margin: 0;
  background: url(../assets/elements/page-bg.jpg) no-repeat center/cover;
  perspective: 1px;
  width: 100%;
}
  
#wrapper {
  margin: 0 auto;
  width: 100%;
}

button {
  padding: 1rem;
  font-size: 1.2rem;
  border-radius: 25%;
  margin: 0 2rem;
  background-color: var(--red);
  color: var(--white);
}

button:hover {
  background-color: var(--green);
  transform: scale(1.1);
  transition: transform 0.3s ease, background-color 0.3s ease;
}

img {
  max-width: 100%;
  height: auto;
}

h1 {
  text-align: center;
}
/* Form */

.form-container {
  width: 80%;
}

form {
  align-items: center;
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: var(--red);
  display: flex;
  flex-direction: column;
  margin: 0 auto 1rem;
  padding: 20px;
}

label {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

input,
textarea {
  padding: 10px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  width: 100%;
}

input[type="submit"] {
  background-color: var(--red);
  color: var(--white);
  font-size: 18px;
  font-weight: bold;
  border: none;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

form input[type="submit"]:hover {
  background-color: var(--green);
}

/* Sections */

section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: var(--background-gray);
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

#payment {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 20px;
 
  margin: 0 auto;
  background-color: #fff;
  background-image: url(../assets/chips/scattered-chips.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

#payment-div, 
#hire-div {
  width: 90%;
  text-align: center;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  position: relative;
  z-index: 2;

}

#payment h1 {
  font-size: 2.5rem;
  color: #4CAF50;
  margin-bottom: 20px;
}

#payment p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 15px;
}

#payment a {
  color: #4CAF50;
  text-decoration: none;
  font-weight: bold;
}

#payment a:hover {
  text-decoration: underline;
}

#payment img {
  width: 250px;
  height: 250px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  animation: float 3s ease-in-out infinite;
}

#payment::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

#payment > * {
  position: relative;
  z-index: 2;
}

/* Footer */
footer {
  background-color: var(--background-gray);
  color: var(--black);
  padding: 2rem 0;
  margin-top: 1rem;
}

.footer-container {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
}

footer a {
  color: var(--black);
}

footer ul {
  list-style: none;
}
.column {
  text-align: center;
  width: 33.33%;
}

.column h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}


#facts {
  background: var(--red) url(../assets/elements/Apple-Pile.png) no-repeat center/cover;
  color: var(--white);
  display: flex;
  flex-direction: column;
}

#fact-container {
  align-items: center;
  background-color: #333333e0;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  margin: 1rem auto;
  width: 90%;
}

#fact-container div {
  align-items: center;
  display: flex;
  gap: 1rem;
}

#fact-container a {
  color: var(--white);
  text-decoration: none;
}

#fact-container .row {
  width: 90%;
  align-items: center;
  justify-content: space-around;
}

#perfect {
  background-image: url(../assets/chips/Chips-Pile.png);
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 500px;
  color: var(--white);
  text-align: center;
  gap: 1rem;
}

#chips h2, 
#whychip h2 {
  font-size:2rem;
}

#morethan {
  background-color: var(--green);
  display: flex;
  align-items: center;
  color: var(--white);
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
}

#blog {
  background-color: var(--black);
  color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
}

#reason {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 1rem;
}

#process {
  background-color: var(--green);
  color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

#reasons {
  justify-content: center;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
}

#reasons div {
  background-color: var(--white);
  border: 1px groove #333;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  padding: 1rem;
  flex-basis: 400px;
}
#reasons div:hover {
  transform: translateY(-2px);
}

#reviews {
  align-items: center;
  background: var(--red) url(../assets/elements/Apple-Pile.png) no-repeat center/cover;
  background-color: var(--red);
  color: var(--white);
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: center;
}

#testis {
  align-items: center;
  display: flex;
  gap: 2rem;
  margin: 50px 0;
  padding: 2rem;
  background-color: var(--background-gray);
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#testis div {
  background-color: #ffffff;
  color: var(--black);
  border-radius: 15px;
  margin-bottom: 20px;
  max-width: 700px;
  padding: 30px;
  text-align: center;
  width: 90%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#testis div:hover {
  transform: translateY(-10px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

#testis p {
  margin: 0;
  font-size: 20px;
  line-height: 1.6;
}


section:nth-child(8) {
  background-color: #333;
  color: #fff;
  padding: 1rem;
}

section:nth-child(9) {
  background-color: #1C5D3B;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

#steps {
  color: var(--green);
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 1rem;
}

#steps div {
  background-color: #f4f4f4;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-basis: 300px;
  margin: 1rem;
  padding: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#steps div:hover {
  transform: translateY(-10px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

#support {
  background-color: #c33748;
}

#heartwarmers {
  background-color: var(--black);
  color: var(--white);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

#assessment {
  background-color: var(--background-gray);
  color: var(--black);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

#heartwarmers p,
#assessment p {
  margin: 0;
  font-size: 20px;
  line-height: 1.6;
}

#heartwarmers img,
#assessment img {
  margin: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#heartwarmers img:hover,
#assessment img:hover {
  transform: translateY(-10px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

#order {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: #1C5D3B;
  color: #fff;
}

#order h2 {
  margin-top: 0;
}

#order p {
  margin-bottom: 2rem;
}

#support {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: var(--red);
  color: var(--white);
} 

.support-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  padding: 2rem;
}

.support-options .card {
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  flex-basis: 300px;
  margin: 1rem;
  padding: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.support-options .card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.support-options .card h3 {
  font-size: 1.5em;
  margin-bottom: 0.5rem;
  color: var(--black);
}

.support-options .card p {
  font-size: 1em;
  color: var(--black);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.support-options .card a {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--green);
  color: var(--white);
  border-radius: 5px;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.support-options .card a:hover {
  background-color: var(--red);
}

#hire {
  background: url("../assets/elements/SoftE2.png") no-repeat center/cover;
  display: flex;
}

#hire-div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  color: var(--black);
  width: 80%;
  margin: 0 auto;
}


.review {
  flex: 0 0 auto;
  scroll-snap-align: start;
  width: 300px;
  border-left: 3px solid #ccc;
  padding: 0.5em 1em;
  margin: 1em 0;
  background-color: #f9f9f9;
  border-radius: 4px;
  text-align: center;
  box-sizing: border-box;
}

.review blockquote {
  font-size: 1em;
  color: #555;
  margin: 0;
}

.review p {
  font-weight: bold;
  margin-top: 0.5em;
  color: #333;
}

blockquote {
font-style: italic;
font-size: 1.2em;
margin: 1em 0;
}

.review-slider {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  /* overflow-x: auto; */
  /* scroll-snap-type: x mandatory; */
  gap: 20px;
  padding: 20px 0;
  /* scrollbar-width: none; */
}

.review-slider .review {
  flex: 0 0 auto;
  scroll-snap-align: start;
  max-width: 300px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1em;
}

@media screen and (max-width: 800px) {
  #payment > div {
    flex-direction: column;
    text-align: center;
  }

  #payment h1 {
    font-size: 2rem;
  }

  #payment img {
    width: 200px;
    height: 200px;
  }
}

@media screen and (max-width: 700px) {
  footer,
  footer ul {
      align-content: center;
      flex-direction: column;
      font-size: 1.2rem;
      padding: 1rem 0;
      text-align: center;
  }
  
  section:nth-child(3) {
    flex-direction: column;
    text-align: center;
  }

  .review-slider {
    flex-direction: column;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .review {
    width: 100%;
    flex: 1 1 100%;
    margin-bottom: 20px;
  }

  #reasons {
    flex-direction: column;
    text-align: center;
  }

  #testis {
    flex-direction: column;
    width: 90%;
  }

  #fact-container div {
    flex-direction: column;
    margin: 0;
    text-align: center;
  }

  .col {
    flex-direction: column;
  }

  .row {
    flex-direction: row;
  }
}
