/**
 * FIXED Google Apps Script for Apple Chip Orders
 * 
 * This version handles both GET and POST requests properly
 * and includes better error handling for undefined parameters.
 */

const SPREADSHEET_ID = '1I_1xSEpWoiTkK-FumlnaEJlft7SbCHlzxIzwnbvlvt0';
const AUTH_TOKEN = 'M1amid4de76'; // Must match PHP code

/**
 * Handle GET requests (for testing)
 */
function doGet(e) {
  try {
    console.log('Received GET request:', e);
    
    // Handle test requests
    if (e.parameter && e.parameter.test) {
      return ContentService
        .createTextOutput(JSON.stringify({
          success: true,
          message: 'GET request successful - Apps Script is working',
          timestamp: new Date().toISOString()
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'GET requests not supported for orders. Use POST.'
      }))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    console.error('Error in doGet:', error);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'GET error: ' + error.message
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Handle POST requests (for actual orders)
 */
function doPost(e) {
  try {
    // Log the incoming request for debugging
    console.log('Received POST request:', e);
    console.log('Event type:', typeof e);
    console.log('Event keys:', e ? Object.keys(e) : 'Event is null/undefined');
    
    // Handle case where event or parameters might be undefined
    if (!e) {
      console.log('Event object is null or undefined');
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'No event object received'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    if (!e.parameters && !e.parameter) {
      console.log('No parameters found in request');
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'No parameters received'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Handle both e.parameters (POST) and e.parameter (GET) formats
    const params = e.parameters || e.parameter || {};
    console.log('Parameters:', params);
    console.log('Parameter keys:', Object.keys(params));
    
    // Validate token - handle both array and string formats
    let token = null;
    if (params.token) {
      token = Array.isArray(params.token) ? params.token[0] : params.token;
    }
    
    if (!token || token !== AUTH_TOKEN) {
      console.log('Unauthorized request - token mismatch. Received:', token, 'Expected:', AUTH_TOKEN);
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Unauthorized - token mismatch'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Check if order_data exists
    let orderDataRaw = null;
    if (params.order_data) {
      orderDataRaw = Array.isArray(params.order_data) ? params.order_data[0] : params.order_data;
    }
    
    if (!orderDataRaw) {
      console.log('No order data provided');
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'No order data provided'
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Parse the order data
    let data;
    try {
      data = JSON.parse(orderDataRaw);
      console.log('Parsed order data:', data);
    } catch (parseError) {
      console.log('Failed to parse order data:', parseError);
      console.log('Raw order data:', orderDataRaw);
      return ContentService
        .createTextOutput(JSON.stringify({
          success: false,
          error: 'Invalid JSON in order data: ' + parseError.message
        }))
        .setMimeType(ContentService.MimeType.JSON);
    }
    
    // Open the spreadsheet
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getActiveSheet();
    
    // Check if this is the first row (add headers if needed)
    if (sheet.getLastRow() === 0) {
      const headers = [
        'Order ID', 'Timestamp', 'Name', 'Email', 'Phone', 
        'Address', 'City', 'State', 'Zip', 'Bags', 
        'Payment Method', 'Message', 'Special Instructions'
      ];
      sheet.appendRow(headers);
      console.log('Added headers to sheet');
    }
    
    // Prepare the row data
    const rowData = [
      data.order_id || '',
      data.timestamp || '',
      data.name || '',
      data.email || '',
      data.phone || '',
      data.address || '',
      data.city || '',
      data.state || '',
      data.zip || '',
      data.bags || '',
      data.payment || '',
      data.message || '',
      data.instructions || ''
    ];
    
    // Append the row to the sheet
    sheet.appendRow(rowData);
    console.log('Successfully added row to sheet');
    
    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        success: true,
        message: 'Order saved successfully',
        order_id: data.order_id,
        timestamp: new Date().toISOString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
    
  } catch (error) {
    console.error('Error in doPost:', error);
    console.error('Error stack:', error.stack);
    return ContentService
      .createTextOutput(JSON.stringify({
        success: false,
        error: 'Server error: ' + error.message
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Test function to verify the script is working
 * You can run this from the Apps Script editor
 */
function testScript() {
  console.log('Starting test...');
  
  const testData = {
    order_id: 'TEST_' + new Date().getTime(),
    timestamp: new Date().toISOString(),
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '************',
    address: '123 Test St',
    city: 'Test City',
    state: 'TS',
    zip: '12345',
    bags: 2,
    payment: 'PayPal',
    message: 'This is a test order',
    instructions: 'Test instructions'
  };
  
  const mockEvent = {
    parameters: {
      token: [AUTH_TOKEN],
      order_data: [JSON.stringify(testData)]
    }
  };
  
  console.log('Mock event:', mockEvent);
  
  const result = doPost(mockEvent);
  const content = result.getContent();
  console.log('Test result:', content);
  
  return content;
}

/**
 * Simple test function that doesn't require parameters
 */
function simpleTest() {
  console.log('Simple test - checking spreadsheet access...');
  
  try {
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getActiveSheet();
    const lastRow = sheet.getLastRow();
    console.log('Spreadsheet accessible. Last row:', lastRow);
    return 'Spreadsheet access successful. Last row: ' + lastRow;
  } catch (error) {
    console.error('Spreadsheet access failed:', error);
    return 'Spreadsheet access failed: ' + error.message;
  }
}
