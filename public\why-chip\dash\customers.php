<?php
// customers.php - Customer Management
require_once 'config/database.php';
require_once 'includes/functions.php';

// Process form submission for adding/updating customers
if ($_SERVER['REQUEST_METHOD'] == 'POST' && is_db_connected()) {
    if (isset($_POST['action'])) {
        try {
            if ($_POST['action'] == 'add') {
                // Add new customer
                $stmt = $pdo->prepare("INSERT INTO Customers (Name, ContactInfo, Notes) VALUES (?, ?, ?)");
                $stmt->execute([$_POST['name'], $_POST['contact'], $_POST['notes']]);
                header("Location: customers.php?success=added");
                exit;
            } elseif ($_POST['action'] == 'update') {
                // Update existing customer
                $stmt = $pdo->prepare("UPDATE Customers SET Name = ?, ContactInfo = ?, Notes = ? WHERE CustomerID = ?");
                $stmt->execute([$_POST['name'], $_POST['contact'], $_POST['notes'], $_POST['customer_id']]);
                header("Location: customers.php?success=updated");
                exit;
            } elseif ($_POST['action'] == 'delete') {
                // Delete customer
                $stmt = $pdo->prepare("DELETE FROM Customers WHERE CustomerID = ?");
                $stmt->execute([$_POST['customer_id']]);
                header("Location: customers.php?success=deleted");
                exit;
            }
        } catch (PDOException $e) {
            error_log("Error in customer form processing: " . $e->getMessage());
            header("Location: customers.php?error=database");
            exit;
        }
    }
}

// Get customer for editing if ID is provided
$editCustomer = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit']) && is_db_connected()) {
    $editCustomer = getById($pdo, 'Customers', 'CustomerID', $_GET['edit']);
}

// Get all customers
$customers = is_db_connected() ? getAll($pdo, 'Customers') : [];

outputHeader("Customer Management");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Customer successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (isset($_GET['error']) && $_GET['error'] == 'database'): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    A database error occurred. Please try again later.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><?= $editCustomer ? 'Edit Customer' : 'Add New Customer' ?></h5>
            </div>
            <div class="card-body">
                <?php if (!is_db_connected()): ?>
                <div class="alert alert-warning">
                    Customer management is unavailable while the database is offline.
                </div>
                <?php else: ?>
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editCustomer ? 'update' : 'add' ?>">
                    <?php if ($editCustomer): ?>
                    <input type="hidden" name="customer_id" value="<?= $editCustomer['CustomerID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="name">Name:</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= $editCustomer['Name'] ?? '' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="contact">Contact Info:</label>
                        <input type="text" class="form-control" id="contact" name="contact" value="<?= $editCustomer['ContactInfo'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?= $editCustomer['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-primary"><?= $editCustomer ? 'Update' : 'Add' ?> Customer</button>
                        <?php if ($editCustomer): ?>
                        <a href="customers.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Customer List</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $customer): ?>
                            <tr>
                                <td><?= $customer['CustomerID'] ?></td>
                                <td><?= htmlspecialchars($customer['Name']) ?></td>
                                <td><?= htmlspecialchars($customer['ContactInfo']) ?></td>
                                <td><?= htmlspecialchars($customer['Notes']) ?></td>
                                <td>
                                    <?php if (is_db_connected()): ?>
                                    <div class="btn-group btn-group-sm">
                                        <a href="customers.php?edit=<?= $customer['CustomerID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this customer?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="customer_id" value="<?= $customer['CustomerID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">Unavailable</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($customers)): ?>
                            <tr>
                                <td colspan="5" class="text-center">No customers found<?= !is_db_connected() ? ' (Database unavailable)' : '' ?></td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>