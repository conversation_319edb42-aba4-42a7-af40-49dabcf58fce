@import url('https://fonts.googleapis.com/css2?family=Lato&family=Marhey&display=swap');

html {
  font-family: 'Marhey', cursive;
  font-size: 1.5rem;
}

* {
  text-align: center;
}

body {
  background-image: url(../images/background.png);
}

div {
  background-color: #d9d9d8;
  border-radius: 5rem;
  width: 80%;
  max-width: 900px;
  margin: 0 auto;
  padding: 1rem 0 1rem 0;
}

nav li {
  display: inline-block;
}

header nav a {
  background-color: #f1e9e9;
  color: purple;
  text-decoration: none;
  border: 1px grey solid;
  border-radius: 5rem;
  padding: .5rem;
}

p {
  padding: 1.5rem;
}
table {
  background-color: darkgray;
  border: 1px grey double;
  border-spacing: .5rem;
  border-radius: 4rem;
  margin: 2rem auto;
  padding: 1rem;
  text-align: center;
}

th,
td {
  background-color: #fff;
  border-radius: 4rem;
  margin: .5rem;
  padding: .5rem;
}

table a {
  color: purple;
  text-decoration: none;
}