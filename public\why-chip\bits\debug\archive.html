<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>April's Beginner Guide to Whole Food Dehydration</title>
    <link rel="stylesheet" href="css/css.css">
    <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon">
    <script src="js/main.js"></script>
        <!-- Chatway -->
    <!-- <script id="chatway" async="true" src="https://cdn.chatway.app/widget.js?id=QGxjxq7wNQi8"></script> -->
    <!-- Clarity -->
    <!-- <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "l6wg07j3fq");
    </script> -->

    <style>
      .blog-preview-card {
        display: inline-block;
        width: 250px;
        height: 600px;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 20px;
        margin: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
        vertical-align: top;
      }

      .blog-preview-card h2 {
        margin-bottom: 10px;
      }

      .blog-preview-card p {
        margin-bottom: 20px;
      }

      .blog-preview-card a {
        align-self: flex-end;
      }
    </style>
  </head>
  <body>
    <a class="skip-link" href="#main" aria-label="Skip to main content">Skip to main content</a>



    <div id="wrapper">
      <script>addHeader(); addFooter();</script>
      <main role="main" id="main" tabindex="-1">
        <div id="blog-preview-cards"></div>
      
      
      
      </main>
    </div>
    <script>
      async function populateBlogPreviewCards() {
        const main = document.getElementById('main');
        fetch('js/apple-facts.json')
          .then(response => response.json())
          .then(data => {
            const factKeys = Object.keys(data['apple-facts']);
            factKeys.forEach(factKey => {
              const fact = data['apple-facts'][factKey];
              const card = document.createElement('div');
              card.className = 'blog-preview-card';
              card.innerHTML = `
                <img src="${fact.image}" alt="${fact.title}">
                <h2>${fact.title}</h2>
                <p>${fact['apple-fact']}</p>
                <a href="apple-facts.html#${factKey}" class="button">Read More</a>
              `;
              main.appendChild(card);
            });
          });
      }
      populateBlogPreviewCards();
    </script>
  </body>
</html>