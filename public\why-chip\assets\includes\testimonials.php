<?php
// Database connection details
$servername = "localhost";
$username = "your_username";
$password = "your_password";
$dbname = "your_database";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Fetch testimonials from the database
$sql = "SELECT id, name, message FROM testimonials ORDER BY id DESC LIMIT 5";
$result = $conn->query($sql);

// Close the connection
$conn->close();
?>

<div class="testimonials-slider">
    <?php if ($result->num_rows > 0): ?>
        <?php while ($row = $result->fetch_assoc()): ?>
            <div class="testimonial">
                <p><?php echo $row['message']; ?></p>
                <p><strong><?php echo $row['name']; ?></strong></p>
            </div>
        <?php endwhile; ?>
    <?php else: ?>
        <p>No testimonials found.</p>
    <?php endif; ?>
</div>