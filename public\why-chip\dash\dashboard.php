<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>


<form action="form-handler.php" method="post">
    <label for="date">Date</label>
    <input type="date" id="date" name="date"><br>
    <label for="type">Type</label>
    <select id="type" name="type">
        <option value="Cash">Cash</option>
        <option value="Paypal">Paypal</option>
    </select><br>
    <label for="amount">Amount</label>
    <input type="number" id="amount" name="amount" step="0.01"><br>
    <label for="notes">Notes</label>
    <input type="text" id="notes" name="notes"><br>
    <input type="submit" value="Submit">
</form>

<?php
// Placeholder connection parameters
$dbhost = 'your_db_host';
$dbuser = 'your_db_user';
$dbpass = 'your_db_pass';
$dbname = 'your_db_name';

// Establish database connection
$connection = mysqli_connect($dbhost, $dbuser, $dbpass, $dbname);

// Check connection
if (!$connection) {
    die("Connection failed: " . mysqli_connect_error());
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $date = $_POST['date'];
    $type = $_POST['type'];
    $amount = $_POST['amount'];
    $notes = $_POST['notes'];

    // Prepare an SQL statement for insertion
    $sql = "INSERT INTO transactions (date, type, amount, notes) VALUES ('$date', '$type', '$amount', '$notes')";

    // Execute the query
    if (mysqli_query($connection, $sql)) {
        echo "New record created successfully";
    } else {
        echo "Error: " . $sql . "<br>" . mysqli_error($connection);
    }
}
?>
    
</body>
</html>