<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hire April Cyr - Web Developer & Culinary Professional</title>
  <!-- <link rel="stylesheet" href="css/css.css"> -->
  <style>
    /* Resume-specific styling */
    .resume-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    .resume-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .resume-section {
      margin-bottom: 2rem;
    }
    
    .skills-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
    }
    
    .skill-category {
      background: #f8f8f8;
      padding: 1rem;
      border-radius: 5px;
    }
    
    .experience-item, .education-item {
      margin-bottom: 1.5rem;
    }
    
    .contact-options {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 2rem;
    }
    
    .appointment-form {
      background: #f8f8f8;
      padding: 2rem;
      border-radius: 5px;
      margin-top: 2rem;
    }

    @media print {
      header, footer, .contact-options, .appointment-form {
        display: none;
      }
      
      .resume-container {
        padding: 0;
      }
    }
  </style>
</head>
<body>
  <!-- Your existing header here -->
  <header>
    <div class="logo">
      <a href="index.html">
        <img src="images/logo.png" alt="April Cyr Logo">
      </a>
    </div>
    <nav>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="portfolio.html">Portfolio</a></li>
        <li><a href="blog.html">Blog</a></li>
        <li><a href="hire.html" class="active">Hire Me</a></li>
        <li><a href="contact.html">Contact</a></li>
      </ul>
    </nav>
  </header>

  <div class="container">
    <main>
      <div class="resume-container">
        <!-- Resume Header -->
        <div class="resume-header">
          <h1>April Cyr (they/them)</h1>
          <p>Candler, NC – 615-502-0384 – <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
        
        <!-- Introduction -->
        <div class="resume-section">
          <p>Hello, I'm April, a dynamic professional with expertise in digital technology, culinary arts, and human services. Fluent in English and Spanish, I hold a BA in Psychology and certifications in Kitchen Ready and Digital Media. I'm working towards Front and Back End Web Developer certifications and CPACC. I combine problem-solving, project management, and a keen awareness of individual needs to drive success.</p>
        </div>
        
        <!-- Skills Section -->
        <div class="resume-section">
          <h2>Skills & Expertise</h2>
          <div class="skills-grid">
            <div class="skill-category">
              <h3>Web Development</h3>
              <ul>
                <li>HTML, CSS, PHP, MySQL</li>
                <li>WordPress, Bricks Builder</li>
                <li>Content Creation</li>
              </ul>
            </div>
            <div class="skill-category">
              <h3>Design & Content</h3>
              <ul>
                <li>Adobe Creative Cloud</li>
                <li>Graphic Design</li>
                <li>Blog Writing & SEO</li>
              </ul>
            </div>
            <div class="skill-category">
              <h3>Technical Support</h3>
              <ul>
                <li>Database Management</li>
                <li>Audio-Visual Support</li>
                <li>IT Helpdesk</li>
              </ul>
            </div>
            <div class="skill-category">
              <h3>Specialized Skills</h3>
              <ul>
                <li>Culinary Arts</li>
                <li>Food Business Management</li>
                <li>Human Services Advocacy</li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- Experience -->
        <div class="resume-section">
          <h2>Experience</h2>
          <div class="experience-item">
            <h3>HelpDesk Associate</h3>
            <p><strong>Asheville Buncombe Technical Community College</strong></p>
            <p>Supported students and faculty, managed tech support tickets, and supervised facilities.</p>
          </div>
          <!-- Add more experience items as needed -->
        </div>
        
        <!-- Education -->
        <div class="resume-section">
          <h2>Credentials</h2>
          <ul>
            <li>BA in Psychology, University of Ohio, Class of 2014</li>
            <li>Kitchen Ready Certification (2019)</li>
            <li>Digital Media Certification Level 1 (expected May)</li>
            <li>Front & Back End Web Developer Certifications (in progress)</li>
            <li>CPACC (Certified Accessibility Pro) Certification (pending exam)</li>
          </ul>
        </div>
        
        <!-- Personal Interests -->
        <div class="resume-section">
          <h2>Personal Interests</h2>
          <p>Administrator of a 12,000-member support group for autistic adults. Passionate about social equity, sustainability, and community advancement.</p>
        </div>
        
        <!-- Contact Options -->
        <div class="contact-options">
          <a href="#appointment-form" class="book-appointment-btn">Book a Consultation</a>
          <a href="newsletter-signup.html" class="newsletter-btn">Subscribe to Newsletter</a>
        </div>
        
        <!-- Appointment Form -->
        <div id="appointment-form" class="appointment-form">
          <h2>Book an Appointment</h2>
          <form method="post" action="appointment-handler.php">
            <div class="form-group">
              <label for="full-name">Full Name</label>
              <input type="text" id="full-name" name="full-name" placeholder="Your Name" required>
            </div>
            
            <div class="form-group">
              <label for="email">Email</label>
              <input type="email" id="email" name="email" placeholder="Your Email" required>
            </div>
            
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone" placeholder="Your Phone Number">
            </div>
            
            <div class="form-group">
              <label for="service">Service Needed</label>
              <select id="service" name="service" required>
                <option value="">Select a Service</option>
                <option value="web-development">Web Development</option>
                <option value="design">Graphic Design</option>
                <option value="content">Content Creation</option>
                <option value="consulting">Technical Consulting</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="message">Project Details</label>
              <textarea id="message" name="message" rows="5" placeholder="Tell me about your project"></textarea>
            </div>
            
            <button type="submit" class="book-appointment-btn">Book Appointment</button>
          </form>
        </div>
      </div>
    </main>
  </div>
</body>
**Note**: References available upon request. -->
