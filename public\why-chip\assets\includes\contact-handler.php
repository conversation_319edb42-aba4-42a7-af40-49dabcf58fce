<?php

// Database connection details
$servername = "localhost";
$username = "your_username";
$password = "your_password";
$dbname = "your_database";

// Email settings
$to = "<EMAIL>"; // Replace with your email address
$subject = "New Message";
$from = "<EMAIL>"; // Replace with your email address
$headers = "From: " . $from . "\r\n" .
           "Reply-To: " . $from . "\r\n" .
           "X-Mailer: PHP/" . phpversion();

// Load PHPMailer classes into the global namespace
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Include required files
require 'path_to_your_project/src/Exception.php';
require 'path_to_your_project/src/PHPMailer.php';
require 'path_to_your_project/src/SMTP.php';

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
  die("Connection failed: " . $conn->connect_error);
}


// Form data handling
if ($_SERVER["REQUEST_METHOD"] == "POST") {
  // Sanitize and validate input data
  $name = trim($_POST["name"]);
  $email = filter_var($_POST["email"], FILTER_SANITIZE_EMAIL);
  $message = trim($_POST["message"]);

  // Check if all required fields are filled
  if (empty($name) || empty($email) || empty($message)) {
    echo "All fields are required.";
    exit;
  }

  // Insert data into SQL table
  $sql = "INSERT INTO messages (name, email, message)
          VALUES (?, ?, ?)";
  $stmt = $conn->prepare($sql);
  $stmt->bind_param("sss", $name, $email, $message);

  if ($stmt->execute()) {
    echo "Message sent successfully!";

    // Send email to admin
    $mail = new PHPMailer(true);
    $mail->isSMTP();
    $mail->Host = 'smtp.example.com'; // Replace with your SMTP server
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; // Replace with your email address
    $mail->Password = 'your_password'; // Replace with your email password
    $mail->SMTPSecure = 'tls';
    $mail->Port = 587;

    $mail->setFrom($from, 'Contact Form');
    $mail->addAddress($to);

    $mail->isHTML(true);
    $mail->Subject = $subject;
    $mail->Body = "New message:\n\n" .
                  "Name: $name\n" .
                  "Email: $email\n" .
                  "Message: $message\n";

    try {
      $mail->send();
      echo "Email sent to admin successfully!";
    } catch (Exception $e) {
      echo "Email sending failed: " . $mail->ErrorInfo;
    }
  } else {
    echo "Error: " . $sql . "
" . $conn->error;
  }

  $stmt->close();
}


$conn->close();

?>

<!-- CREATE TABLE messages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  message TEXT NOT NULL
); -->

<!-- Make sure to replace the following placeholders with your actual information:

$to: Replace with the email address where you want to receive the contact form messages.
$from: Replace with the email address from which you want to send the emails.
smtp.example.com: Replace with your SMTP server address.
<EMAIL>: Replace with your email address.
your_password: Replace with your email password.
With this updated code, the contact form data will be inserted into the messages table when the user submits the form. The form handler will only execute when the form is submitted using the POST method. Make sure to configure your SMTP server settings correctly in the PHPMailer code. -->