<!-- <?php

// function fetch_testimonials($conn) {
//     $testimonials = [];
//     $sql = "SELECT name, message FROM testimonials";
//     $result = $conn->query($sql);
// 
//     if ($result->num_rows > 0) {
//         while($row = $result->fetch_assoc()) {
//             $testimonials[] = $row;
//         }
//     }
// 
//     return $testimonials;
// }
// 
// $testimonials = fetch_testimonials($conn);
// ?>
// 
// <div id="testimonial-slider">
//     <?php foreach($testimonials as $testimonial): ?>
//         <div class="testis">
//             <p><?php echo htmlspecialchars($testimonial['message']); ?></p>
//    <h4><?php echo htmlspecialchars($testimonial['name']); ?></h4>
//         </div>
//     <?php endforeach; ?>
</div> -->
