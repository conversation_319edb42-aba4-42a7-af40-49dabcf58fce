'use strict';

// Global Variables

const blogPostsContainer = document.getElementById('blog-post-section');
const carouselControls = document.querySelector('.carousel-controls');
const left = carouselControls.querySelector('.prev');
const right = carouselControls.querySelector('.next');
let currentSlide = 0;

/**
 * Inject Single Post - Injects the single post into the page
 * 
 */

if (document.URL.includes('single.html?')) {
  document.addEventListener('DOMContentLoaded', function() {
    renderPost(postId);
  });
  let postId = new URLSearchParams(window.location.search).get('id');
}

/**
 * createBlogPostSection - Creates the blog post section
 *
 * @param   {string}  containerId  - The ID of the container element where the blog posts will be inserted
 * @param   {string}  jsonDataUrl  - The URL of the JSON data file containing the blog posts
 *
 */

async function createBlogPostSection(containerId, jsonDataUrl) {
  const blogPostsContainer = document.getElementById(containerId);
  const response = await fetch(jsonDataUrl);
  const data = await response.json();

  if (Array.isArray(data.posts)) {
    const blogCardContainer = document.createElement('div');
    blogCardContainer.className = 'blog-card-container';

    const cardSection = document.createElement('div');
    cardSection.className = 'blog-card-section';

    data.posts.forEach((post) => {
      const blogPostCard = document.createElement('a');
      blogPostCard.href = `single.html?id=${post.id}`; 
      blogPostCard.id = `${post.id}`;
      blogPostCard.className = 'blog-card';
      blogPostCard.innerHTML = `
        <img src="${post.preview.thumbnail}" alt="${post.preview?.altText}">
        <h3>${post.title}</h3>
      `;
      cardSection.appendChild(blogPostCard);
    });

    blogCardContainer.appendChild(cardSection);
    blogPostsContainer.appendChild(blogCardContainer);
  }
}

/**
 * renderPost(postId) - Renders a blog post
 *
 * @param   {number}  postId  - The ID of the post to render
 *
 */

function renderPost(postId) {
  fetch('js/blog-posts.json')
    .then((response) => response.json())
    .then((data) => {
      const post = data.posts.find(post => post.id === postId);
      if (!post) {
        console.error(`Post not found with ID ${postId}`);
        return;
      }
      
      const markdownFilePath = `markdown/${post.content}`;
      fetch(markdownFilePath)
        .then((response) => response.text())
        .then((markdownContent) => {
          const htmlContent = marked.parse(markdownContent);
          const postHtml = `
            <div class="post-container">
              <h1 class="post-title">${post.title}</h1>
              <img src="${post.preview.thumbnail}" alt="${post.title}" width="325" height="325" class="post-image">
              <p class="post-description">${post.blurb}</p>
              <p class="post-tags">Tags: ${post.preview.tags.join(', ')}</p>
              ${htmlContent}
            </div>
            `;

          const postElement = document.getElementById('post-content');
          postElement.innerHTML = postHtml;
        })
        .catch((error) => {
          console.error(`Error fetching Markdown content: ${error}`);
        });
    })
    .catch((error) => {
      console.error(`Error fetching post data: ${error}`);
    });
}
function goToSlide(direction) {
  const cardSection = blogPostsContainer.querySelector('.blog-card-section');

  const cardWidth = cardSection.children[0].offsetWidth;
  const numCards = cardSection.children.length;

  if (direction === 'next') {
    currentSlide = Math.min(currentSlide + 1, numCards - 1);
  } else {
    currentSlide = Math.max(currentSlide - 1, 0);
  }

  const newOffset = -currentSlide * cardWidth;
  cardSection.style.transform = `translateX(${newOffset}px)`;
  console.log('New Offset:', newOffset);
  console.log('Current Slide:', currentSlide);
}

right.addEventListener('click', () => {
  goToSlide('next');
});

left.addEventListener('click', () => {
  goToSlide('prev');
});

document.addEventListener('DOMContentLoaded', function() {
  createBlogPostSection('blog-post-section', 'js/apple-facts.json');
});

