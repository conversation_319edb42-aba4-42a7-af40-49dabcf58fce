@import url('https://fonts.googleapis.com/css2?family=Exo+2:ital,wght@0,100..900;1,100..900&family=Satisfy&family=Spinnaker&display=swap');


/* // font */
.spinnaker-regular {
  font-family: "Spinnaker", sans-serif;
  font-weight: 400;
  font-style: normal;
}

/* // <uniquifier>: Use a unique and descriptive class name
// <weight>: Use a value from 100 to 900 */

.exo-2-font {
  font-family: "Exo 2", sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

.satisfy-regular {
  font-family: "Satisfy", cursive;
  font-weight: 400;
  font-style: normal;
}

/* // Variables */
:root {
  --green: #149954; 
  --red: #E4312b;
  --black: #000000;
  --white: #fff;
  
  --error-red: #FF3333;
  --warning-orange: #FF9900;
  --success-green: #2ECC40;
  --info-blue: #0077CC;
  --highlight-yellow: #FFCC00;
  --background-gray: #F5F5F5; */
}
/* // $green: #149954;
// $red: #E4312b;
// $black: #000000;
// $white: #fff;

// $error-red: #FF3333;
// $warning-orange: #FF9900;
// $success-green: #2ECC40;
// $info-blue: #0077CC;
// $highlight-yellow: #FFCC00;
// $background-gray: #F5F5F5; */


/* // Typography */
body {
  font-family: Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.02em;
  color: $text-color;
}

h1 {
  font-family: 'Roboto', sans-serif;
  font-size: 3rem;
  line-height: 1.2;
  letter-spacing: 0.05em;
  color: $primary-color;
}

a {
  color: $primary-color;
  text-decoration: none;

  &:hover,
  &:focus {
    color: darken($primary-color, 10%);
    text-decoration: underline;
  }
}

body {
  cursor: url(../hamsa24.svg), auto;
}

// Layout
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

// Components

// Buttons
.btn {
  display: inline-block;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.02em;
  text-decoration: none;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;

  &-primary {
    color: $background-color;
    background-color: $primary-color;
    border: 1px solid $primary-color;

    &:hover,
    &:focus {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }

  &-secondary {
    color: $primary-color;
    background-color: $secondary-color;
    border: 1px solid $secondary-color;

    &:hover,
    &:focus {
      background-color: darken($secondary-color, 10%);
      border-color: darken($secondary-color, 10%);
    }
  }
}

// Form elements
// ... (add your form element styles here)

// Navigation
// ... (add your navigation styles here)

// Tables
// ... (add your table styles here)

// Cards
.cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-gap: 20px;
    padding: 2rem;
  }

  .card img {
    width: 100%;
  }

  .card {
    background: $background-gray;
    padding: 1rem;
    border-radius: 2px;
  }

// Alerts
// ... (add your alert styles here)

// Modals
// ... (add your modal styles here)

// Breadcrumbs
// ... (add your breadcrumb styles here)

// Utilities
// ... (add your utility styles here)