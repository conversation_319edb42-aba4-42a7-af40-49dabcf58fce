<?php include_once('./connect.php');

if ($conn->connect_error) { die("Connection failed: " . $conn->connect_error); }

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $message = $_POST['message'];

    $sql = "INSERT INTO testimonials (name, email, message) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);

    if ($stmt === false) { die("Prepare failed: " . $conn->error); }

    if (!$stmt->bind_param("sss", $name, $email, $message)) { die("Binding parameters failed: " . $stmt->error); }

    if (!$stmt->execute()) { echo "Execute failed: " . $stmt->error; } else { echo "New record created successfully"; }
    $stmt->close();
}

$conn->close();

header('Location: ' . $_SERVER['HTTP_REFERER']);
    