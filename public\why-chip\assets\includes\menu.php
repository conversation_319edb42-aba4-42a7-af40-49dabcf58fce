<nav>
    <span id="menu-trigger">&#9776;</span>
    <ul>
      <li><a href="#">Link 1</a></li>
      <li><a href="#">Link 2</a></li>
      <li><a href="#">Link 3</a></li>
      <li><a href="#">Link 4</a></li>
      <li><a href="#">Link 5</a></li>
    </ul>
  </nav>
  
  <style>
      nav {
        border-radius: 0 15px 15px 0;
        box-shadow: 5px 5px 15px #000;
        display: flex;
        left: 0;
        position: absolute;
        top: 100px;
        transform: translateX(-250px);
        transition: .5s ease-in-out;
        width: 300px;
      }
      
      nav:hover,
      nav:active {
        transform: translateX(0);
      }
      
      #menu-trigger {
        background-color: #fff;
        border: 5px solid #000;
        border-radius: 0 15px 15px 0;
        cursor: pointer;
        order: 2;
        padding: 1rem;
      }

      button {
  border: 2px solid #333;
  display: block;
  margin: 1rem;
  padding: .5rem;
  transition: .5s;
}

button:hover {
  background-color: #006400;
  color: #fff;
}

nav {
  border-radius: 0 15px 15px 0;
  box-shadow: 5px 5px 15px #000;
  display: flex;
  left: 0;
  position: absolute;
  top: 100px;
  transform: translateX(-250px);
  transition: .5s ease-in-out;
  width: 300px;
}

nav:hover,
nav:active {
  transform: translateX(0);
}

#menu-trigger {
  background-color: #fff;
  border: 5px solid #000;
  border-radius: 0 15px 15px 0;
  cursor: pointer;
  order: 2;
  padding: 1rem;
}

ul {
  background-color: #333;
  border: 5px solid #000;
  border-left: none;
  flex: 1;
  list-style: none;
  margin: 0;
  padding: 1rem;
}

a {
  border-bottom: 1px dotted #fff;
  margin-bottom: 1rem;
  border-radius: 15px;
  color: #fff;
  display: block;
  padding: 1rem;
  text-decoration: none;
  transition: .5s;
}

a:hover {
  background-color: #fff;
  color: #000;
}
  </style>