When uploaded set directory permissions to 755 - readable by web server. Consider protecting directory with .htaccess file. 

# April's Apple Chips Order Form

A standalone PHP order form for apple chip holiday orders with responsive design and form handling.

## Features

- **Responsive Design**: Side-by-side layout on desktop, mobile-friendly with scroll-to-form button
- **Form Validation**: Server-side validation with sanitization
- **CSV Export**: Orders saved to spreadsheet format
- **Email Notifications**: HTML email notifications sent to admin
- **Security**: CSRF protection and rate limiting
- **User Experience**: Form persistence on errors, success/error messages

## Setup Instructions

### 1. Configuration

Edit `config.php` and update the following settings:

```php
// IMPORTANT: Change this to your email address
define('ADMIN_EMAIL', '<EMAIL>');

// Adjust other settings as needed
define('MAX_BAGS_PER_ORDER', 10);
define('SHIPPING_COST', 7.50);
```

### 2. File Permissions

Ensure the web server can write to the orders directory:

```bash
chmod 755 orders/
chmod 644 orders/orders.csv  # if file exists
```

### 3. Email Configuration

For better email delivery, consider:

- Setting up SPF/DKIM records for your domain
- Using a dedicated SMTP service (requires additional PHP configuration)
- Testing email functionality with your hosting provider

### 4. Security Considerations

- Keep `config.php` secure and don't expose sensitive settings
- Consider moving the `orders/` directory outside the web root
- Regularly backup the orders CSV file
- Monitor for spam submissions

## File Structure

```
├── order.php          # Main order form page
├── config.php         # Configuration settings
├── orders/            # Directory for CSV files (auto-created)
│   └── orders.csv     # Order data (auto-created)
└── README.md          # This file
```

## Customization

### Styling

The CSS is embedded in `order.php`. Key customization areas:

- **Colors**: Update the color scheme in the CSS variables
- **Layout**: Modify the grid layout for different breakpoints
- **Typography**: Change fonts and sizing

### Form Fields

To add/remove form fields:

1. Update the form HTML in `order.php`
2. Add validation rules in the PHP processing section
3. Update the CSV headers in `saveToSpreadsheet()` function
4. Update the email template in `sendEmailNotification()` function

### Payment Methods

Update the payment methods in `config.php`:

```php
$payment_methods = [
    'PayPal' => 'https://paypal.me/YourUsername',
    'CashApp' => 'https://cash.app/$YourUsername',
    'Venmo' => 'https://venmo.com/YourUsername'
];
```

## Testing

1. **Form Submission**: Test with valid and invalid data
2. **Email Delivery**: Verify emails are received
3. **CSV Generation**: Check that orders.csv is created and populated
4. **Mobile Responsiveness**: Test on various screen sizes
5. **Security**: Test CSRF protection and rate limiting

## Troubleshooting

### Emails Not Sending

- Check your hosting provider's email configuration
- Verify the `ADMIN_EMAIL` setting in `config.php`
- Check server error logs for mail() function errors
- Consider using SMTP instead of PHP's mail() function

### CSV File Not Created

- Check file permissions on the orders directory
- Verify the web server can write to the directory
- Check error logs for file creation issuess

### Form Not Submitting

- Check for PHP errors in server logs
- Verify all required fields are filled
- Check CSRF token functionality
- Ensure rate limiting isn't blocking submissions

## Support

For issues or customization help, check:

1. Server error logs
2. Browser developer console for JavaScript errors
3. PHP error reporting settings
4. File permissions and ownership

## License

This project is open source and available for modification and redistribution.
