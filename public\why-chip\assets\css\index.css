

/* @font-face {
    font-family: 'Kadwa';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/kadwa/v10/rnCm-x5V0g7ipiTAT8M.ttf) format('truetype');
  }
  
  /************* General Styles **************/
  * {
    box-sizing: border-box;
  }
  html {
    font: 17px/1.5 'Tajawal', sans-serif;
  }
  body {
    margin: 0;
  }

  img {
    height: auto;
    max-width: 100%;
  }
  /******** Widths for anything needed to be centered on page **************/
  #page-header,
  nav ul,
  #pool-columns,
  #blurbs,
  footer p {
    margin: 0 auto;
    max-width: 1183px;
    width: 90%;
    align-items:center;
  }
  /******** Page Header *************/
  #page-header {
    display: flex;
    flex-wrap: wrap;
  }
  #page-header div:first-child {
    flex: 1;
    order: 2;
    text-align: center;
  }
  #page-header div:last-child {
    flex: 1;
    margin-right: 0.5rem;
    order: 1;
  }
  #page-header h1 {
    font-size: 36px;
    margin: 0;
  }
  #page-header p {
    font: bold small-caps 20px/1.5 'Kadwa', serif;
    margin: 0;
  }
  #page-header img {
    display: block;
    margin: 0 auto;
  }
  /************** Navbar ****************/
  
  /************ Main Header **************/
  /* main header {
    background-color: #f2f2f1;
    border-bottom: #999 1px solid;
    border-top: #999 1px solid;
    margin-top: 1rem;
    padding: 1rem;
    text-align: center;
  }
  main header h2 {
    font-size: 20px;
    margin: 0;
  }
  main header p {
    font-size: 18px;
    margin: 0;
  } */
  /*********** Pool columns **************/
  #pool-columns {
    display: flex;
    flex-wrap: wrap;
    padding: 0 1rem;
  }
  #pool-columns section {
    flex: 1;
    margin-right: 1rem;
  }
  #pool-columns section:last-child {
    margin-right: 0;
  }
  /************** Specials ************/
  #special {
    background-color: #f2f2f1;
    border-bottom: #333 1px solid;
    border-top: #333 1px solid;
    padding: 1rem;
    text-align: center;
  }
  #special h2 {
    font-size: 5vw;
    margin: 0;
  }
  #special p {
    margin-top: 0;
  }
  /***************** Blurbs **************/
  #blurbs {
    display: flex;
    flex-wrap: wrap;
    padding: 0 1rem;
  }
  #blurbs section {
    flex: 1;
    margin-right: 1rem;
    text-align: center;
  }
  #blurbs section:last-child {
    margin-right: 0;
  }
  #blurbs img {
    border: #005583 5px solid;
    border-radius: 50%;
  }

  /************** Fluid text starts at 320px *************/
  @media screen and (min-width: 320px) {
    #page-header h1 {
      font-size: calc(36px + (76 - 36) * (100vw - 320px) / (1200 - 320));
    }
    #page-header p {
      font-size: calc(20px + (48 - 20) * (100vw - 320px) / (1200 - 320));
    }
    main header h2 {
      font-size: calc(20px + (48 - 20) * (100vw - 320px) / (1200 - 320));
    }
    main header p {
      font-size: calc(18px + (36 - 18) * (100vw - 320px) / (1200 - 320));
    }
    .phone {
      font-size: calc(24px + (48 - 24) * (100vw - 320px) / (1200 - 320));
    }
  }
  /************ 794px and narrower ****************/
  @media screen and (max-width: 794px) {
    nav ul {
      display: none;
      width: 100%;
    }
    nav #nav-trigger,
    nav #nav-checkbox:checked + ul,
    nav li,
    nav a {
      display: block;
    }
    nav a {
      border-bottom: #000 2px solid;
      min-height: 45px;
    }
  }
  /************** 569px and narrower ****************/
  @media screen and (max-width: 569px) {
    #page-header {
      flex-direction: column-reverse;
    }
    #pool-columns img {
      float: none;
      margin: 0;
    }
    #blurbs {
      display: block;
    }
    #blurbs p {
      padding-bottom: 2rem;
    }
  }
  /************** Largest font thresholds *************/
  @media screen and (min-width: 1200px) {
    #page-header h1 {
      font-size: 76px;
    }
    #page-header p {
      font-size: 48px;
    }
    main header h2 {
      font-size: 48px;
    }
    main header p {
      font-size: 36px;
    }
    .phone {
      font-size: 48px;
    }
  }
  