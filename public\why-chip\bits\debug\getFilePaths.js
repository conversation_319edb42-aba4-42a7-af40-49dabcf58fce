const fs = require("fs");
const path = require("path");

const mainDirectory = "./assets/gallery"; // Replace with your main directory path

function getFilePaths(directoryPath) {
  const files = fs.readdirSync(directoryPath);

  const filePaths = [];

  files.forEach((file) => {
    const filePath = path.join(directoryPath, file);

    if (fs.lstatSync(filePath).isDirectory()) {
      filePaths.push(...getFilePaths(filePath));
    } else {
      filePaths.push(filePath);
    }
  });

  return filePaths;
}

const filePaths = getFilePaths(mainDirectory);

console.log(filePaths);

// node getFilePaths.js in terminal..
