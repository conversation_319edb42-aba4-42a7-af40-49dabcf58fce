<?php
// Include the database connection file
include_once('./php/connect.php');

// Initialize variables for form data and error messages
$name = $email = '';
$errors = [];

// Check if the form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate and sanitize the name
    if (empty($_POST['name'])) {
        $errors['name'] = 'Name is required.';
    } else {
        $name = trim($_POST['name']);
        if (!preg_match("/^[a-zA-Z-' ]*$/", $name)) {
            $errors['name'] = 'Only letters and spaces are allowed.';
        }
    }

    // Validate and sanitize the email
    if (empty($_POST['email'])) {
        $errors['email'] = 'Email is required.';
    } else {
        $email = trim($_POST['email']);
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email format.';
        }
    }

    // If there are no errors, insert the data into the database
    if (empty($errors)) {
        try {
            // Prepare the SQL statement
            $stmt = $conn->prepare("INSERT INTO subscribers (name, email) VALUES (:name, :email)");
            // Bind parameters
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':email', $email);
            $stmt->execute();

            // Redirect to a success page or display a success message
            header('Location: success.html'); // Replace with your success page
            exit();
        } catch (PDOException $e) {
            // Handle database errors
            if ($e->getCode() === '23000') { // Duplicate email error
                $errors['email'] = 'This email is already subscribed.';
            } else {
                $errors['database'] = 'An error occurred. Please try again later.';
            }
        }
    }
}

// If there are errors, display them and keep the form values
if (!empty($errors)) {
    // You can pass the errors and form data back to the form page using sessions or query parameters
    session_start();
    $_SESSION['errors'] = $errors;
    $_SESSION['form_data'] = ['name' => $name, 'email' => $email];
    header('Location: index.php'); // Redirect back to the form page
    exit();
}

// Close the database connection
$conn = null;

// Redirect to a success page or display a success message
header('Location: index.php'); 
?>