<?php
// config.php - Database connection configuration
$host = 'az1-ss110.a2hosting.com';
$dbname = 'aachipsc_chips_dash';
$username = 'aachipsc_chips'; // Change to your MySQL username
$password = 'gjA2dpwI+rhM'; // Change to your MySQL password

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Functions for CRUD operations
function getAll($pdo, $table) {
    $stmt = $pdo->prepare("SELECT * FROM $table ORDER BY CreatedAt DESC");
    $stmt->execute();
    return $stmt->fetchAll();
}

function getById($pdo, $table, $idField, $id) {
    $stmt = $pdo->prepare("SELECT * FROM $table WHERE $idField = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function deleteRecord($pdo, $table, $idField, $id) {
    $stmt = $pdo->prepare("DELETE FROM $table WHERE $idField = ?");
    return $stmt->execute([$id]);
}

// Common header function
function outputHeader($title) {
    echo '<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . $title . ' - Apple Chip Business</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
        <style>
            body { padding-top: 20px; }
            .container { max-width: 960px; }
            .table-responsive { margin-top: 20px; }
            .form-group { margin-bottom: 15px; }
            .nav-pills .nav-link.active { background-color: #28a745; }
        </style>
    </head>
    <body>
        <div class="container">
            <header class="mb-4">
                <h1 class="display-4">' . $title . '</h1>
                <nav class="nav nav-pills flex-column flex-sm-row my-3">
                    <a class="flex-sm-fill text-sm-center nav-link" href="index.php">Dashboard</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="customers.php">Customers</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="orders.php">Orders</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="production.php">Production</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="inventory.php">Inventory</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="transactions.php">Finances</a>
                </nav>
            </header>';
}

// Common footer function
function outputFooter() {
    echo '</div>
    <footer class="container mt-5 pt-3 border-top text-center text-muted">
        <p>&copy; ' . date('Y') . ' Apple Chip Business</p>
    </footer>
    </body>
    </html>';
}
?>

<?php
// index.php - Dashboard
require_once 'config.php';

// Get summary counts
$customersCount = $pdo->query("SELECT COUNT(*) FROM Customers")->fetchColumn();
$ordersCount = $pdo->query("SELECT COUNT(*) FROM Orders")->fetchColumn();
$productionCount = $pdo->query("SELECT COUNT(*) FROM Production")->fetchColumn();
$inventoryCount = $pdo->query("SELECT COUNT(*) FROM Inventory")->fetchColumn();

// Get recent orders
$recentOrders = $pdo->query("SELECT o.*, c.Name AS CustomerName 
                            FROM Orders o 
                            LEFT JOIN Customers c ON o.CustomerID = c.CustomerID 
                            ORDER BY o.OrderDate DESC LIMIT 5")->fetchAll();

// Get financial summary
$financialSummary = $pdo->query("SELECT 
                                SUM(CASE WHEN Type = 'Income' THEN Amount ELSE 0 END) AS TotalIncome,
                                SUM(CASE WHEN Type = 'Expense' THEN Amount ELSE 0 END) AS TotalExpense,
                                SUM(CASE WHEN Type = 'Income' THEN Amount ELSE -Amount END) AS NetProfit
                                FROM FinancialTransactions")->fetch();

// Get production summary
$productionData = $pdo->query("SELECT SUM(QuantityBags) AS TotalBags FROM Production")->fetch();

outputHeader("Dashboard");
?>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">Customers</h5>
                <p class="card-text display-6"><?= $customersCount ?></p>
                <a href="customers.php" class="text-white">Manage →</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">Orders</h5>
                <p class="card-text display-6"><?= $ordersCount ?></p>
                <a href="orders.php" class="text-white">Manage →</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5 class="card-title">Production</h5>
                <p class="card-text display-6"><?= $productionCount ?></p>
                <a href="production.php" class="text-white">Manage →</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <h5 class="card-title">Inventory Items</h5>
                <p class="card-text display-6"><?= $inventoryCount ?></p>
                <a href="inventory.php" class="text-dark">Manage →</a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Financial Summary</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>Total Income</th>
                        <td>$<?= number_format($financialSummary['TotalIncome'], 2) ?></td>
                    </tr>
                    <tr>
                        <th>Total Expenses</th>
                        <td>$<?= number_format($financialSummary['TotalExpense'], 2) ?></td>
                    </tr>
                    <tr class="table-<?= $financialSummary['NetProfit'] >= 0 ? 'success' : 'danger' ?>">
                        <th>Net Profit</th>
                        <td>$<?= number_format($financialSummary['NetProfit'], 2) ?></td>
                    </tr>
                </table>
                <a href="transactions.php" class="btn btn-outline-primary btn-sm">View All Transactions</a>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Production Summary</h5>
            </div>
            <div class="card-body">
                <p class="display-4 text-center"><?= $productionData['TotalBags'] ?? 0 ?></p>
                <p class="text-center text-muted">Total Bags Produced</p>
                <a href="production.php" class="btn btn-outline-primary btn-sm">View Production Log</a>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5>Recent Orders</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Customer</th>
                        <th>Quantity</th>
                        <th>Amount</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentOrders as $order): ?>
                    <tr>
                        <td><?= $order['OrderDate'] ?></td>
                        <td><?= $order['CustomerName'] ?? 'N/A' ?></td>
                        <td><?= $order['Quantity'] ?></td>
                        <td>$<?= number_format($order['Amount'], 2) ?></td>
                        <td><span class="badge bg-<?= $order['PaymentStatus'] == 'Paid' ? 'success' : ($order['PaymentStatus'] == 'Pending' ? 'warning' : 'danger') ?>">
                            <?= $order['PaymentStatus'] ?>
                        </span></td>
                    </tr>
                    <?php endforeach; ?>
                    <?php if (empty($recentOrders)): ?>
                    <tr>
                        <td colspan="5" class="text-center">No recent orders found</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <a href="orders.php" class="btn btn-primary">View All Orders</a>
    </div>
</div>

<?php outputFooter(); ?>

<?php
// customers.php - Customer Management
require_once 'config.php';

// Process form submission for adding/updating customers
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add') {
            // Add new customer
            $stmt = $pdo->prepare("INSERT INTO Customers (Name, ContactInfo, Notes) VALUES (?, ?, ?)");
            $stmt->execute([$_POST['name'], $_POST['contact'], $_POST['notes']]);
            header("Location: customers.php?success=added");
            exit;
        } elseif ($_POST['action'] == 'update') {
            // Update existing customer
            $stmt = $pdo->prepare("UPDATE Customers SET Name = ?, ContactInfo = ?, Notes = ? WHERE CustomerID = ?");
            $stmt->execute([$_POST['name'], $_POST['contact'], $_POST['notes'], $_POST['customer_id']]);
            header("Location: customers.php?success=updated");
            exit;
        } elseif ($_POST['action'] == 'delete') {
            // Delete customer
            $stmt = $pdo->prepare("DELETE FROM Customers WHERE CustomerID = ?");
            $stmt->execute([$_POST['customer_id']]);
            header("Location: customers.php?success=deleted");
            exit;
        }
    }
}

// Get customer for editing if ID is provided
$editCustomer = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editCustomer = getById($pdo, 'Customers', 'CustomerID', $_GET['edit']);
}

// Get all customers
$customers = getAll($pdo, 'Customers');

outputHeader("Customer Management");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Customer successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><?= $editCustomer ? 'Edit Customer' : 'Add New Customer' ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editCustomer ? 'update' : 'add' ?>">
                    <?php if ($editCustomer): ?>
                    <input type="hidden" name="customer_id" value="<?= $editCustomer['CustomerID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="name">Name:</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= $editCustomer['Name'] ?? '' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="contact">Contact Info:</label>
                        <input type="text" class="form-control" id="contact" name="contact" value="<?= $editCustomer['ContactInfo'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?= $editCustomer['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-primary"><?= $editCustomer ? 'Update' : 'Add' ?> Customer</button>
                        <?php if ($editCustomer): ?>
                        <a href="customers.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Customer List</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $customer): ?>
                            <tr>
                                <td><?= $customer['CustomerID'] ?></td>
                                <td><?= htmlspecialchars($customer['Name']) ?></td>
                                <td><?= htmlspecialchars($customer['ContactInfo']) ?></td>
                                <td><?= htmlspecialchars($customer['Notes']) ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="customers.php?edit=<?= $customer['CustomerID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this customer?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="customer_id" value="<?= $customer['CustomerID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($customers)): ?>
                            <tr>
                                <td colspan="5" class="text-center">No customers found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>

<?php
// orders.php - Order Management
require_once 'config.php';

// Process form submission for adding/updating orders
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add') {
            // Add new order
            $stmt = $pdo->prepare("INSERT INTO Orders (CustomerID, OrderDate, DeliveryDate, Quantity, Amount, PaymentStatus, PaymentMethod, Notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $_POST['customer_id'] ?: null, 
                $_POST['order_date'], 
                $_POST['delivery_date'] ?: null, 
                $_POST['quantity'], 
                $_POST['amount'], 
                $_POST['payment_status'], 
                $_POST['payment_method'], 
                $_POST['notes']
            ]);
            header("Location: orders.php?success=added");
            exit;
        } elseif ($_POST['action'] == 'update') {
            // Update existing order
            $stmt = $pdo->prepare("UPDATE Orders SET CustomerID = ?, OrderDate = ?, DeliveryDate = ?, Quantity = ?, Amount = ?, PaymentStatus = ?, PaymentMethod = ?, Notes = ? WHERE OrderID = ?");
            $stmt->execute([
                $_POST['customer_id'] ?: null, 
                $_POST['order_date'], 
                $_POST['delivery_date'] ?: null, 
                $_POST['quantity'], 
                $_POST['amount'], 
                $_POST['payment_status'], 
                $_POST['payment_method'], 
                $_POST['notes'],
                $_POST['order_id']
            ]);
            header("Location: orders.php?success=updated");
            exit;
        } elseif ($_POST['action'] == 'delete') {
            // Delete order
            $stmt = $pdo->prepare("DELETE FROM Orders WHERE OrderID = ?");
            $stmt->execute([$_POST['order_id']]);
            header("Location: orders.php?success=deleted");
            exit;
        }
    }
}

// Get order for editing if ID is provided
$editOrder = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $stmt = $pdo->prepare("SELECT * FROM Orders WHERE OrderID = ?");
    $stmt->execute([$_GET['edit']]);
    $editOrder = $stmt->fetch();
}

// Get all orders with customer names
$stmt = $pdo->prepare("SELECT o.*, c.Name AS CustomerName FROM Orders o LEFT JOIN Customers c ON o.CustomerID = c.CustomerID ORDER BY o.OrderDate DESC");
$stmt->execute();
$orders = $stmt->fetchAll();

// Get all customers for dropdown
$stmt = $pdo->prepare("SELECT CustomerID, Name FROM Customers ORDER BY Name");
$stmt->execute();
$customers = $stmt->fetchAll();

outputHeader("Order Management");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Order successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><?= $editOrder ? 'Edit Order' : 'Add New Order' ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editOrder ? 'update' : 'add' ?>">
                    <?php if ($editOrder): ?>
                    <input type="hidden" name="order_id" value="<?= $editOrder['OrderID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="customer_id">Customer:</label>
                        <select class="form-control" id="customer_id" name="customer_id">
                            <option value="">-- Select Customer --</option>
                            <?php foreach ($customers as $customer): ?>
                            <option value="<?= $customer['CustomerID'] ?>" <?= ($editOrder && $editOrder['CustomerID'] == $customer['CustomerID']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($customer['Name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="order_date">Order Date:</label>
                        <input type="date" class="form-control" id="order_date" name="order_date" value="<?= $editOrder['OrderDate'] ?? date('Y-m-d') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="delivery_date">Delivery Date:</label>
                        <input type="date" class="form-control" id="delivery_date" name="delivery_date" value="<?= $editOrder['DeliveryDate'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="quantity">Quantity (Bags):</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" value="<?= $editOrder['Quantity'] ?? '1' ?>" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="amount">Amount ($):</label>
                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="<?= $editOrder['Amount'] ?? '8.00' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment_status">Payment Status:</label>
                        <select class="form-control" id="payment_status" name="payment_status" required>
                            <option value="Pending" <?= ($editOrder && $editOrder['PaymentStatus'] == 'Pending') ? 'selected' : '' ?>>Pending</option>
                            <option value="Paid" <?= ($editOrder && $editOrder['PaymentStatus'] == 'Paid') ? 'selected' : '' ?>>Paid</option>
                            <option value="Cancelled" <?= ($editOrder && $editOrder['PaymentStatus'] == 'Cancelled') ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment_method">Payment Method:</label>
                        <select class="form-control" id="payment_method" name="payment_method">
                            <option value="">-- Select Method --</option>
                            <option value="Cash" <?= ($editOrder && $editOrder['PaymentMethod'] == 'Cash') ? 'selected' : '' ?>>Cash</option>
                            <option value="PayPal" <?= ($editOrder && $editOrder['PaymentMethod'] == 'PayPal') ? 'selected' : '' ?>>PayPal</option>
                            <option value="Check" <?= ($editOrder && $editOrder['PaymentMethod'] == 'Check') ? 'selected' : '' ?>>Check</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?= $editOrder['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-success"><?= $editOrder ? 'Update' : 'Add' ?> Order</button>
                        <?php if ($editOrder): ?>
                        <a href="orders.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Order List</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Qty</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><?= $order['OrderID'] ?></td>
                                <td><?= $order['OrderDate'] ?></td>
                                <td><?= htmlspecialchars($order['CustomerName'] ?? 'N/A') ?></td>
                                <td><?= $order['Quantity'] ?></td>
                                <td>$<?= number_format($order['Amount'], 2) ?></td>
                                <td><span class="badge bg-<?= $order['PaymentStatus'] == 'Paid' ? 'success' : ($order['PaymentStatus'] == 'Pending' ? 'warning' : 'danger') ?>">
                                    <?= $order['PaymentStatus'] ?>
                                </span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="orders.php?edit=<?= $order['OrderID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this order?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="order_id" value="<?= $order['OrderID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($orders)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No orders found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>

<?php
// production.php - Production Management
require_once 'config.php';

// Process form submission for adding/updating production records
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add') {
            // Add new production record
            $stmt = $pdo->prepare("INSERT INTO Production (Date, QuantityBags, AppleWeightUsed, BatchNumber, Notes) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                $_POST['date'], 
                $_POST['quantity_bags'], 
                $_POST['apple_weight'] ?: null, 
                $_POST['batch_number'] ?: null, 
                $_POST['notes']
            ]);
            header("Location: production.php?success=added");
            exit;
        } elseif ($_POST['action'] == 'update') {
            // Update existing production record
            $stmt = $pdo->prepare("UPDATE Production SET Date = ?, QuantityBags = ?, AppleWeightUsed = ?, BatchNumber = ?, Notes = ? WHERE ProductionID = ?");
            $stmt->execute([
                $_POST['date'], 
                $_POST['quantity_bags'], 
                $_POST['apple_weight'] ?: null, 
                $_POST['batch_number'] ?: null, 
                $_POST['notes'],
                $_POST['production_id']
            ]);
            header("Location: production.php?success=updated");
            exit;
        } elseif ($_POST['action'] == 'delete') {
            // Delete production record
            $stmt = $pdo->prepare("DELETE FROM Production WHERE ProductionID = ?");
            $stmt->execute([$_POST['production_id']]);
            header("Location: production.php?success=deleted");
            exit;
        }
    }
}

// Get production record for editing if ID is provided
$editProduction = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editProduction = getById($pdo, 'Production', 'ProductionID', $_GET['edit']);
}

// Get all production records
$productionRecords = getAll($pdo, 'Production');

outputHeader("Production Management");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Production record successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><?= $editProduction ? 'Edit Production Record' : 'Add Production Record' ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editProduction ? 'update' : 'add' ?>">
                    <?php if ($editProduction): ?>
                    <input type="hidden" name="production_id" value="<?= $editProduction['ProductionID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="date">Production Date:</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?= $editProduction['Date'] ?? date('Y-m-d') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="quantity_bags">Quantity (Bags):</label>
                        <input type="number" class="form-control" id="quantity_bags" name="quantity_bags" value="<?= $editProduction['QuantityBags'] ?? '' ?>" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="apple_weight">Apple Weight Used (lbs):</label>
                        <input type="number" step="0.01" class="form-control" id="apple_weight" name="apple_weight" value="<?= $editProduction['AppleWeightUsed'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="batch_number">Batch Number:</label>
                        <input type="text" class="form-control" id="batch_number" name="batch_number" value="<?= $editProduction['BatchNumber'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?= $editProduction['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-info"><?= $editProduction ? 'Update' : 'Add' ?> Record</button>
                        <?php if ($editProduction): ?>
                        <a href="production.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Production Records</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Bags</th>
                                <th>Apple Weight</th>
                                <th>Batch</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($productionRecords as $record): ?>
                            <tr>
                                <td><?= $record['ProductionID'] ?></td>
                                <td><?= $record['Date'] ?></td>
                                <td><?= $record['QuantityBags'] ?></td>
                                <td><?= $record['AppleWeightUsed'] ? $record['AppleWeightUsed'] . ' lbs' : 'N/A' ?></td>
                                <td><?= htmlspecialchars($record['BatchNumber'] ?? 'N/A') ?></td>
                                <td><?= htmlspecialchars($record['Notes'] ?? '') ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="production.php?edit=<?= $record['ProductionID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this record?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="production_id" value="<?= $record['ProductionID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($productionRecords)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No production records found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>

<?php
// inventory.php - Inventory Management
require_once 'config.php';

// Process form submission for adding/updating inventory
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add') {
            // Add new inventory item
            $stmt = $pdo->prepare("INSERT INTO Inventory (Date, Type, Quantity, Source, Cost, Weight, Notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $_POST['date'], 
                $_POST['type'], 
                $_POST['quantity'], 
                $_POST['source'] ?: null, 
                $_POST['cost'] ?: null, 
                $_POST['weight'] ?: null, 
                $_POST['notes']
            ]);
            header("Location: inventory.php?success=added");
            exit;
        } elseif ($_POST['action'] == 'update') {
            // Update existing inventory item
            $stmt = $pdo->prepare("UPDATE Inventory SET Date = ?, Type = ?, Quantity = ?, Source = ?, Cost = ?, Weight = ?, Notes = ? WHERE InventoryID = ?");
            $stmt->execute([
                $_POST['date'], 
                $_POST['type'], 
                $_POST['quantity'], 
                $_POST['source'] ?: null, 
                $_POST['cost'] ?: null, 
                $_POST['weight'] ?: null, 
                $_POST['notes'],
                $_POST['inventory_id']
            ]);
            header("Location: inventory.php?success=updated");
            exit;
        } elseif ($_POST['action'] == 'delete') {
            // Delete inventory item
            $stmt = $pdo->prepare("DELETE FROM Inventory WHERE InventoryID = ?");
            $stmt->execute([$_POST['inventory_id']]);
            header("Location: inventory.php?success=deleted");
            exit;
        }
    }
}

// Get inventory item for editing if ID is provided
$editInventory = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editInventory = getById($pdo, 'Inventory', 'InventoryID', $_GET['edit']);
}

// Get all inventory items
$inventoryItems = getAll($pdo, 'Inventory');

// Get inventory summary
$inventorySummary = $pdo->query("SELECT Type, SUM(Quantity) as TotalQuantity, SUM(Weight) as TotalWeight FROM Inventory GROUP BY Type")->fetchAll();

outputHeader("Inventory Management");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Inventory item successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Inventory Summary</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Total Quantity</th>
                                <th>Total Weight</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inventorySummary as $item): ?>
                            <tr>
                                <td><?= htmlspecialchars($item['Type']) ?></td>
                                <td><?= $item['TotalQuantity'] ?></td>
                                <td><?= $item['TotalWeight'] ? $item['TotalWeight'] . ' lbs' : 'N/A' ?></td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($inventorySummary)): ?>
                            <tr>
                                <td colspan="3" class="text-center">No inventory data found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5><?= $editInventory ? 'Edit Inventory Item' : 'Add Inventory Item' ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editInventory ? 'update' : 'add' ?>">
                    <?php if ($editInventory): ?>
                    <input type="hidden" name="inventory_id" value="<?= $editInventory['InventoryID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="date">Date:</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?= $editInventory['Date'] ?? date('Y-m-d') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Type:</label>
                        <select class="form-control" id="type" name="type" required>
                            <option value="">-- Select Type --</option>
                            <option value="Apples" <?= ($editInventory && $editInventory['Type'] == 'Apples') ? 'selected' : '' ?>>Apples</option>
                            <option value="Bags" <?= ($editInventory && $editInventory['Type'] == 'Bags') ? 'selected' : '' ?>>Bags</option>
                            <option value="Labels" <?= ($editInventory && $editInventory['Type'] == 'Labels') ? 'selected' : '' ?>>Labels</option>
                            <option value="Supplies" <?= ($editInventory && $editInventory['Type'] == 'Supplies') ? 'selected' : '' ?>>Supplies</option>
                            <option value="Other" <?= ($editInventory && $editInventory['Type'] == 'Other') ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="quantity">Quantity:</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" value="<?= $editInventory['Quantity'] ?? '' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="weight">Weight (lbs, for apples):</label>
                        <input type="number" step="0.01" class="form-control" id="weight" name="weight" value="<?= $editInventory['Weight'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="source">Source:</label>
                        <input type="text" class="form-control" id="source" name="source" value="<?= $editInventory['Source'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="cost">Cost ($):</label>
                        <input type="number" step="0.01" class="form-control" id="cost" name="cost" value="<?= $editInventory['Cost'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?= $editInventory['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-warning"><?= $editInventory ? 'Update' : 'Add' ?> Item</button>
                        <?php if ($editInventory): ?>
                        <a href="inventory.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Inventory Items</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Quantity</th>
                                <th>Source</th>
                                <th>Cost</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inventoryItems as $item): ?>
                            <tr>
                                <td><?= $item['InventoryID'] ?></td>
                                <td><?= $item['Date'] ?></td>
                                <td><?= htmlspecialchars($item['Type']) ?></td>
                                <td><?= $item['Quantity'] ?> <?= $item['Weight'] ? '(' . $item['Weight'] . ' lbs)' : '' ?></td>
                                <td><?= htmlspecialchars($item['Source'] ?? 'N/A') ?></td>
                                <td><?= $item['Cost'] ? '$' . number_format($item['Cost'], 2) : 'N/A' ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="inventory.php?edit=<?= $item['InventoryID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this item?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="inventory_id" value="<?= $item['InventoryID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($inventoryItems)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No inventory items found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>

<?php
// transactions.php - Financial Transactions Management
require_once 'config.php';

// Process form submission for adding/updating financial transactions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add') {
            // Add new transaction
            $stmt = $pdo->prepare("INSERT INTO FinancialTransactions (Date, Type, Category, PaymentMethod, Amount, RelatedOrderID, RelatedInventoryID, Notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $_POST['date'], 
                $_POST['type'], 
                $_POST['category'], 
                $_POST['payment_method'] ?: null, 
                $_POST['amount'], 
                $_POST['related_order_id'] ?: null, 
                $_POST['related_inventory_id'] ?: null, 
                $_POST['notes']
            ]);
            header("Location: transactions.php?success=added");
            exit;
        } elseif ($_POST['action'] == 'update') {
            // Update existing transaction
            $stmt = $pdo->prepare("UPDATE FinancialTransactions SET Date = ?, Type = ?, Category = ?, PaymentMethod = ?, Amount = ?, RelatedOrderID = ?, RelatedInventoryID = ?, Notes = ? WHERE TransactionID = ?");
            $stmt->execute([
                $_POST['date'], 
                $_POST['type'], 
                $_POST['category'], 
                $_POST['payment_method'] ?: null, 
                $_POST['amount'], 
                $_POST['related_order_id'] ?: null, 
                $_POST['related_inventory_id'] ?: null, 
                $_POST['notes'],
                $_POST['transaction_id']
            ]);
            header("Location: transactions.php?success=updated");
            exit;
        } elseif ($_POST['action'] == 'delete') {
            // Delete transaction
            $stmt = $pdo->prepare("DELETE FROM FinancialTransactions WHERE TransactionID = ?");
            $stmt->execute([$_POST['transaction_id']]);
            header("Location: transactions.php?success=deleted");
            exit;
        }
    }
}

// Get transaction for editing if ID is provided
$editTransaction = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editTransaction = getById($pdo, 'FinancialTransactions', 'TransactionID', $_GET['edit']);
}

// Get all transactions
$transactions = getAll($pdo, 'FinancialTransactions');

// Get all orders for dropdown
$stmt = $pdo->prepare("SELECT o.OrderID, CONCAT('Order #', o.OrderID, ' - ', COALESCE(c.Name, 'N/A'), ' (', o.OrderDate, ')') AS OrderInfo 
                       FROM Orders o 
                       LEFT JOIN Customers c ON o.CustomerID = c.CustomerID 
                       ORDER BY o.OrderDate DESC");
$stmt->execute();
$orders = $stmt->fetchAll();

// Get all inventory items for dropdown
$stmt = $pdo->prepare("SELECT InventoryID, CONCAT('Inventory #', InventoryID, ' - ', Type, ' (', Date, ')') AS InventoryInfo 
                       FROM Inventory 
                       ORDER BY Date DESC");
$stmt->execute();
$inventoryItems = $stmt->fetchAll();

outputHeader("Financial Transactions");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Transaction successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><?= $editTransaction ? 'Edit Transaction' : 'Add New Transaction' ?></h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editTransaction ? 'update' : 'add' ?>">
                    <?php if ($editTransaction): ?>
                    <input type="hidden" name="transaction_id" value="<?= $editTransaction['TransactionID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="date">Date:</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?= $editTransaction['Date'] ?? date('Y-m-d') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Type:</label>
                        <select class="form-control" id="type" name="type" required>
                            <option value="Income" <?= ($editTransaction && $editTransaction['Type'] == 'Income') ? 'selected' : '' ?>>Income</option>
                            <option value="Expense" <?= ($editTransaction && $editTransaction['Type'] == 'Expense') ? 'selected' : '' ?>>Expense</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="category">Category:</label>
                        <select class="form-control" id="category" name="category" required>
                            <option value="">-- Select Category --</option>
                            <optgroup label="Income">
                                <option value="Sales" <?= ($editTransaction && $editTransaction['Category'] == 'Sales') ? 'selected' : '' ?>>Sales</option>
                                <option value="Donation" <?= ($editTransaction && $editTransaction['Category'] == 'Donation') ? 'selected' : '' ?>>Donation</option>
                                <option value="Other Income" <?= ($editTransaction && $editTransaction['Category'] == 'Other Income') ? 'selected' : '' ?>>Other Income</option>
                            </optgroup>
                            <optgroup label="Expense">
                                <option value="Supplies" <?= ($editTransaction && $editTransaction['Category'] == 'Supplies') ? 'selected' : '' ?>>Supplies</option>
                                <option value="Apples" <?= ($editTransaction && $editTransaction['Category'] == 'Apples') ? 'selected' : '' ?>>Apples</option>
                                <option value="Packaging" <?= ($editTransaction && $editTransaction['Category'] == 'Packaging') ? 'selected' : '' ?>>Packaging</option>
                                <option value="Rent" <?= ($editTransaction && $editTransaction['Category'] == 'Rent') ? 'selected' : '' ?>>Rent</option>
                                <option value="Utilities" <?= ($editTransaction && $editTransaction['Category'] == 'Utilities') ? 'selected' : '' ?>>Utilities</option>
                                <option value="Transportation" <?= ($editTransaction && $editTransaction['Category'] == 'Transportation') ? 'selected' : '' ?>>Transportation</option>
                                <option value="Other Expense" <?= ($editTransaction && $editTransaction['Category'] == 'Other Expense') ? 'selected' : '' ?>>Other Expense</option>
                            </optgroup>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="amount">Amount ($):</label>
                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="<?= $editTransaction['Amount'] ?? '' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment_method">Payment Method:</label>
                        <select class="form-control" id="payment_method" name="payment_method">
                            <option value="">-- Select Method --</option>
                            <option value="Cash" <?= ($editTransaction && $editTransaction['PaymentMethod'] == 'Cash') ? 'selected' : '' ?>>Cash</option>
                            <option value="PayPal" <?= ($editTransaction && $editTransaction['PaymentMethod'] == 'PayPal') ? 'selected' : '' ?>>PayPal</option>
                            <option value="Check" <?= ($editTransaction && $editTransaction['PaymentMethod'] == 'Check') ? 'selected' : '' ?>>Check</option>
                            <option value="Bank Transfer" <?= ($editTransaction && $editTransaction['PaymentMethod'] == 'Bank Transfer') ? 'selected' : '' ?>>Bank Transfer</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="related_order_id">Related Order:</label>
                        <select class="form-control" id="related_order_id" name="related_order_id">
                            <option value="">-- Select Order --</option>
                            <?php foreach ($orders as $order): ?>
                            <option value="<?= $order['OrderID'] ?>" <?= ($editTransaction && $editTransaction['RelatedOrderID'] == $order['OrderID']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($order['OrderInfo']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="related_inventory_id">Related Inventory:</label>
                        <select class="form-control" id="related_inventory_id" name="related_inventory_id">
                            <option value="">-- Select Inventory --</option>
                            <?php foreach ($inventoryItems as $item): ?>
                            <option value="<?= $item['InventoryID'] ?>" <?= ($editTransaction && $editTransaction['RelatedInventoryID'] == $item['InventoryID']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($item['InventoryInfo']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?= $editTransaction['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-primary"><?= $editTransaction ? 'Update' : 'Add' ?> Transaction</button>
                        <?php if ($editTransaction): ?>
                        <a href="transactions.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Financial Transactions</h5>
                <div class="btn-group btn-group-sm">
                    <a href="transactions.php" class="btn btn-outline-primary">All</a>
                    <a href="transactions.php?filter=income" class="btn btn-outline-success">Income Only</a>
                    <a href="transactions.php?filter=expense" class="btn btn-outline-danger">Expenses Only</a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Category</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transactions as $transaction): ?>
                            <?php
                                // Apply filter if set
                                if (isset($_GET['filter']) && strtolower($_GET['filter']) != strtolower($transaction['Type'])) {
                                    continue;
                                }
                            ?>
                            <tr>
                                <td><?= $transaction['TransactionID'] ?></td>
                                <td><?= $transaction['Date'] ?></td>
                                <td><span class="badge bg-<?= $transaction['Type'] == 'Income' ? 'success' : 'danger' ?>">
                                    <?= $transaction['Type'] ?>
                                </span></td>
                                <td><?= htmlspecialchars($transaction['Category']) ?></td>
                                <td>$<?= number_format($transaction['Amount'], 2) ?></td>
                                <td><?= htmlspecialchars($transaction['PaymentMethod'] ?? 'N/A') ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="transactions.php?edit=<?= $transaction['TransactionID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this transaction?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="transaction_id" value="<?= $transaction['TransactionID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($transactions)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No transactions found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>