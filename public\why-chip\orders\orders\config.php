<?php
/**
 * Configuration file for Apple Chip Orders
 * 
 * IMPORTANT: Change these settings before using the order form
 */

// Email Configuration
define('ADMIN_EMAIL', '<EMAIL>'); // CHANGE THIS to your email address
define('FROM_EMAIL', 'noreply@' . ($_SERVER['HTTP_HOST'] ?? 'localhost'));

// Order Configuration
define('MAX_BAGS_PER_ORDER', 10);
define('SHIPPING_COST', 7.50);

// File Configuration
define('ORDERS_CSV_FILE', 'orders.csv');
define('ORDERS_DIRECTORY', __DIR__ . '/orders/');

// Google Sheets Configuration
define('GOOGLE_WEBAPP_URL', 'https://script.google.com/macros/s/AKfycbzaEz6AG9IC5YxygrMSQSspxDEkqXm8-Rh48Q_hLFlhv56od9ODebeC78nzU<PERSON>hn-_5mw/exec'); // Your deployment URL
define('GOOGLE_APP_TOKEN', 'M1amid4de76'); // Must match Apps Script

// Payment Methods
$payment_methods = [
    'PayPal' => 'https://paypal.me/BBushwick',
    'CashApp' => 'https://cash.app/$AppleChipKitchen'
];

// Create orders directory if it doesn't exist
if (!file_exists(ORDERS_DIRECTORY)) {
    mkdir(ORDERS_DIRECTORY, 0755, true);
}

// Security settings
define('ENABLE_CSRF_PROTECTION', true);
define('SESSION_TIMEOUT', 3600); // 1 hour

// Email settings for better delivery
define('EMAIL_HEADERS', [
    'MIME-Version' => '1.0',
    'Content-Type' => 'text/html; charset=UTF-8',
    'X-Mailer' => 'PHP/' . phpversion()
]);

// Validation rules
define('VALIDATION_RULES', [
    'name' => ['required' => true, 'max_length' => 100],
    'email' => ['required' => true, 'type' => 'email'],
    'message' => ['required' => true, 'max_length' => 1000],
    'phone' => ['max_length' => 20],
    'address' => ['max_length' => 200],
    'city' => ['max_length' => 100],
    'state' => ['max_length' => 50],
    'zip' => ['max_length' => 20],
    'bags' => ['required' => true, 'type' => 'integer', 'min' => 1, 'max' => MAX_BAGS_PER_ORDER],
    'instructions' => ['max_length' => 500],
    'payment' => ['required' => true, 'in_array' => array_keys($payment_methods)]
]);

// Success and error messages
define('MESSAGES', [
    'success' => 'Your order has been submitted successfully! You will receive a confirmation email shortly.',
    'error_general' => 'There was an error processing your order. Please try again.',
    'error_email' => 'Order saved but email notification failed. We will still process your order.',
    'error_save' => 'There was an error saving your order. Please try again.',
    'error_validation' => 'Please correct the errors below and try again.'
]);

// Rate limiting (simple implementation)
define('RATE_LIMIT_ENABLED', true);
define('RATE_LIMIT_REQUESTS', 5); // Max requests per time window
define('RATE_LIMIT_WINDOW', 300); // Time window in seconds (5 minutes)

?>
