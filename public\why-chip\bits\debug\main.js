'use strict';

const readMoreLink = document.getElementById('read-more-link');
const coolFactElement = document.getElementById('cool-fact');

/**
 * getRandomFact - Gets a random fact from the JSON file
 *
 * @return  {Promise}  A promise that resolves to the random fact
 */

async function getRandomFact() {
  const response = await fetch('js/apple-facts.json');
  const data = await response.json();
  const facts = Object.values(data['apple-facts']);
  const randomIndex = Math.floor(Math.random() * facts.length);
  return facts[randomIndex];
}

/**
 * updateCoolFacts - Updates the cool facts
 *
 * @param   {Object}  fact  The fact to update
 */

function updateCoolFacts(fact) {
  const coolFactElement = document.getElementById('cool-fact');
  coolFactElement.textContent = fact['apple-fact'];
}

/**
 * goToPost - Goes to the post
 * 
 */

function goToPost() {
  const readMoreLink = document.getElementById('read-more-link');
  readMoreLink.addEventListener('click', (e) => {
    e.preventDefault();
    getRandomFact().then(fact => {
      window.location.href = `${fact['hyperlink-slug']}`;
    });
  });
}

/**
 * addheader - Adds the header to the page
 * 
 */

function addHeader() {
  const headerElement = document.createElement('header');
  headerElement.innerHTML = `
    <div>
      <img src="../../assets/logos/logo125.png" alt="">
      <h1>AAChips</h1>
    </div>
    <nav role="navigation" class="navbar">
      <input id="nav-box" type="checkbox">
      <label for="nav-box" id="nav-trigger">&equiv; Menu</label>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="archive.html">Blog</a>
        </li>
        <li><a href="hire.html">Hire</a>
        </li>
        <li><a href="gallery.html">Gallery</a></li>
        <li><a href="support.html">Support</a></li>
        <li>
          <div class="dropdown">
            <button class="dropbtn">Projects ⬇️
              <i class="fa fa-caret-down"></i>
            </button>
            <div class="dropdown-content">
              <a href="https://aachips.co/heartwarmers-pr/index.html">Heartwarmers</a>
              <a href="diy-guide.html">Chipping Guide</a>
              <a href="https://aachips.co/assessment/index.html">Assessment for Adult Competencies</a>
              <a href="https://aachips.co/lingua/index.html">Kitchen Lingua / Ladino Kitchen</a>
            </div>
          </div>
        </li>
      </ul>
    </nav>
  `;
  const skipLink = document.querySelector('.skip-link');
  skipLink.insertAdjacentElement('afterend', headerElement);
}

/**
 * addFooter - Adds the footer to the page
 * 
 */

function addFooter() {
  const footerElement = document.createElement('footer');
  footerElement.innerHTML = `
    <div class="footer-container">
      <div class="column">
        <h3>Subscribe</h3>
        <form id="subscribe-form" method="post" action="">
          <div class="form-group">
            <label for="name">Name</label>
            <input type="text" id="name" name="name" placeholder="Your Name" required>
          </div>
          <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" placeholder="Your Email" required>
          </div>
          <button type="submit" class="subscribe-btn">Subscribe</button>
        </form>
      </div>
  
      <div class="column">
        <h3>Quick Links</h3>
        <ul>
          <li><a href="#">Home</a></li>
          <li><a href="#">Services</a></li>
          <li><a href="#">About Us</a></li>
          <li><a href="#">Contact Us</a></li>
          <li><a href="#">Privacy Policy</a></li>
        </ul>
      </div>
  
      <div class="column">
        <h3>Support</h3>
        <p>Make a one-time or recurring donation</p>
        <a href="#" class="donate-btn">Donate</a>
      </div>
    </div>
  `;

  document.body.appendChild(footerElement);
}
