<?php
// Functions for CRUD operations with database connection checks

function getAll($pdo, $table) {
    if (!is_db_connected()) {
        return get_fallback_data($table);
    }
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM $table ORDER BY CreatedAt DESC");
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error in getAll function: " . $e->getMessage());
        return [];
    }
}

function getById($pdo, $table, $idField, $id) {
    if (!is_db_connected()) {
        return null;
    }
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM $table WHERE $idField = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Error in getById function: " . $e->getMessage());
        return null;
    }
}

function deleteRecord($pdo, $table, $idField, $id) {
    if (!is_db_connected()) {
        return false;
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM $table WHERE $idField = ?");
        return $stmt->execute([$id]);
    } catch (PDOException $e) {
        error_log("Error in deleteRecord function: " . $e->getMessage());
        return false;
    }
}

// Common header function
function outputHeader($title) {
    echo '<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . $title . ' - Apple Chip Business</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
        <style>
            body { padding-top: 20px; }
            .container { max-width: 960px; }
            .table-responsive { margin-top: 20px; }
            .form-group { margin-bottom: 15px; }
            .nav-pills .nav-link.active { background-color: #28a745; }
        </style>
    </head>
    <body>
        <div class="container">
            <header class="mb-4">
                <h1 class="display-4">' . $title . '</h1>
                <nav class="nav nav-pills flex-column flex-sm-row my-3">
                    <a class="flex-sm-fill text-sm-center nav-link" href="index.php">Dashboard</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="customers.php">Customers</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="orders.php">Orders</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="production.php">Production</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="inventory.php">Inventory</a>
                    <a class="flex-sm-fill text-sm-center nav-link" href="transactions.php">Finances</a>
                </nav>';
    
    // Display database connection status
    if (!is_db_connected()) {
        echo '<div class="alert alert-warning" role="alert">
            <strong>Database connection is currently unavailable.</strong> Some features may be limited.
        </div>';
    }
    
    echo '</header>';
}

// Common footer function
function outputFooter() {
    echo '</div>
    <footer class="container mt-5 pt-3 border-top text-center text-muted">
        <p>&copy; ' . date('Y') . ' Apple Chip Business</p>
    </footer>
    </body>
    </html>';
}
?>