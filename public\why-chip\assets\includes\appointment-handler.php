<!-- First, download the PHPMailer library from the official GitHub repository: https://github.com/PHPMailer/PHPMailer

Extract the downloaded archive and copy the src folder to your project directory.

Update the form handler code to include the PHPMailer library: -->

<?php

// Database connection details
$servername = "localhost";
$username = "your_username";
$password = "your_password";
$dbname = "your_database";

// Email settings
$to = "<EMAIL>"; // Replace with your email address
$subject = "New Appointment Booking";
$from = "<EMAIL>"; // Replace with your email address
$headers = "From: " . $from . "\r\n" .
           "Reply-To: " . $from . "\r\n" .
           "X-Mailer: PHP/" . phpversion();

// Load PHPMailer classes into the global namespace
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Include required files
require 'path_to_your_project/src/Exception.php';
require 'path_to_your_project/src/PHPMailer.php';
require 'path_to_your_project/src/SMTP.php';

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
  die("Connection failed: " . $conn->connect_error);
}


// Form data handling
if ($_SERVER["REQUEST_METHOD"] == "POST") {
  // Sanitize and validate input data
  $fullName = trim($_POST["full-name"]);
  $email = filter_var($_POST["email"], FILTER_SANITIZE_EMAIL);
  $phone = trim($_POST["phone"]);
  $appointmentReason = $_POST["appointment-reason"];
  $meetingLocation = trim($_POST["meeting-location"]);
  $meetingExpectations = trim($_POST["meeting-expectations"]);
  $budget = trim($_POST["budget"]);
  $consultation = $_POST["consultation"] === "Yes" ? 1 : 0;
  $websiteServices = $_POST["website-services"];
  $culinaryPlanning = $_POST["culinary-planning"];

  // Check if all required fields are filled
  if (empty($fullName) || empty($email) || empty($appointmentReason) || empty($meetingLocation) || empty($meetingExpectations) || empty($budget)) {
    echo "All fields are required.";
    exit;
  }

  // Insert data into SQL table
  $sql = "INSERT INTO appointments (full_name, email, phone, appointment_reason, meeting_location, meeting_expectations, budget, consultation, website_services, culinary_planning)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
  $stmt = $conn->prepare($sql);
  $stmt->bind_param("ssssssiii", $fullName, $email, $phone, $appointmentReason, $meetingLocation, $meetingExpectations, $budget, $consultation, $websiteServices, $culinaryPlanning);

  if ($stmt->execute()) {
    echo "Appointment booked successfully!";

    // Send email to admin
    $mail = new PHPMailer(true);
    $mail->isSMTP();
    $mail->Host = 'smtp.example.com'; // Replace with your SMTP server
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; // Replace with your email address
    $mail->Password = 'your_password'; // Replace with your email password
    $mail->SMTPSecure = 'tls';
    $mail->Port = 587;

    $mail->setFrom($from, 'Appointment Booking');
    $mail->addAddress($to);

    $mail->isHTML(true);
    $mail->Subject = $subject;
    $mail->Body = "New appointment booking:\n\n" .
                  "Full Name: $fullName\n" .
                  "Email: $email\n" .
                  "Phone: $phone\n" .
                  "Appointment Reason: $appointmentReason\n" .
                  "Meeting Location: $meetingLocation\n" .
                  "Meeting Expectations: $meetingExpectations\n" .
                  "Budget: $budget\n" .
                  "Consultation: " . ($consultation ? "Yes" : "No") . "\n" .
                  "Website Services: $websiteServices\n" .
                  "Culinary Planning: $culinaryPlanning\n";

    try {
      $mail->send();
      echo "Email sent to admin successfully!";
    } catch (Exception $e) {
      echo "Email sending failed: " . $mail->ErrorInfo;
    }

    // Send confirmation email to user
    $confirmationSubject = "Appointment Booking Confirmation";
    $confirmationBody = "Thank you for booking an appointment with us!\n\n" .
                       "Full Name: $fullName\n" .
                       "Email: $email\n" .
                       "Phone: $phone\n" .
                       "Appointment Reason: $appointmentReason\n" .
                       "Meeting Location: $meetingLocation\n" .
                       "Meeting Expectations: $meetingExpectations\n" .
                       "Budget: $budget\n" .
                       "Consultation: " . ($consultation ? "Yes" : "No") . "\n" .
                       "Website Services: $websiteServices\n" .
                       "Culinary Planning: $culinaryPlanning\n";

    try {
      $confirmationMail = new PHPMailer(true);
      $confirmationMail->isSMTP();
      $confirmationMail->Host = 'smtp.example.com'; // Replace with your SMTP server
      $confirmationMail->SMTPAuth = true;
      $confirmationMail->Username = '<EMAIL>'; // Replace with your email address
      $confirmationMail->Password = 'your_password'; // Replace with your email password
      $confirmationMail->SMTPSecure = 'tls';
      $confirmationMail->Port = 587;

      $confirmationMail->setFrom($from, 'Appointment Booking');
      $confirmationMail->addAddress($email);

      $confirmationMail->isHTML(true);
      $confirmationMail->Subject = $confirmationSubject;
      $confirmationMail->Body = $confirmationBody;

      $confirmationMail->send();
      echo "Confirmation email sent successfully!";
    } catch (Exception $e) {
      echo "Confirmation email sending failed: " . $confirmationMail->ErrorInfo;
    }
  } else {
    echo "Error: " . $sql . "
" . $conn->error;
  }

  $stmt->close();
}


$conn->close();

?>

<!-- Make sure to replace the following placeholders with your actual information:

$to: Replace with the email address where you want to receive the appointment booking notifications.
$from: Replace with the email address from which you want to send the emails.
smtp.example.com: Replace with your SMTP server address.
<EMAIL>: Replace with your email address.
your_password: Replace with your email password.
With this updated code, an email with the submitted information will be sent to the specified email address ($to), and a confirmation email will be sent to the person filling out the form ($email). Make sure to configure your SMTP server settings correctly in the PHPMailer code. -->

<!-- CREATE TABLE appointments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  appointment_reason VARCHAR(50) NOT NULL,
  meeting_location VARCHAR(100) NOT NULL,
  meeting_expectations TEXT NOT NULL,
  budget DECIMAL(8,2) NOT NULL,
  consultation BOOLEAN NOT NULL,
  website_services VARCHAR(50) NOT NULL,
  culinary_planning VARCHAR(50) NOT NULL
);
	 -->