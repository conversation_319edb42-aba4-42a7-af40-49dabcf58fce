<?php
// inventory.php - Inventory Management
require_once 'config/database.php';
require_once 'includes/functions.php';

// Process form submission for adding/updating inventory
if ($_SERVER['REQUEST_METHOD'] == 'POST' && is_db_connected()) {
    if (isset($_POST['action'])) {
        try {
            if ($_POST['action'] == 'add') {
                // Add new inventory item
                $stmt = $pdo->prepare("INSERT INTO Inventory (Date, Type, Quantity, Source, Cost, Weight, Notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_POST['date'], 
                    $_POST['type'], 
                    $_POST['quantity'], 
                    $_POST['source'] ?: null, 
                    $_POST['cost'] ?: null, 
                    $_POST['weight'] ?: null, 
                    $_POST['notes']
                ]);
                header("Location: inventory.php?success=added");
                exit;
            } elseif ($_POST['action'] == 'update') {
                // Update existing inventory item
                $stmt = $pdo->prepare("UPDATE Inventory SET Date = ?, Type = ?, Quantity = ?, Source = ?, Cost = ?, Weight = ?, Notes = ? WHERE InventoryID = ?");
                $stmt->execute([
                    $_POST['date'], 
                    $_POST['type'], 
                    $_POST['quantity'], 
                    $_POST['source'] ?: null, 
                    $_POST['cost'] ?: null, 
                    $_POST['weight'] ?: null, 
                    $_POST['notes'],
                    $_POST['inventory_id']
                ]);
                header("Location: inventory.php?success=updated");
                exit;
            } elseif ($_POST['action'] == 'delete') {
                // Delete inventory item
                $stmt = $pdo->prepare("DELETE FROM Inventory WHERE InventoryID = ?");
                $stmt->execute([$_POST['inventory_id']]);
                header("Location: inventory.php?success=deleted");
                exit;
            }
        } catch (PDOException $e) {
            error_log("Error in inventory form processing: " . $e->getMessage());
            header("Location: inventory.php?error=database");
            exit;
        }
    }
}

// Initialize variables
$editInventory = null;
$inventoryItems = [];
$inventorySummary = [];

if (is_db_connected()) {
    try {
        // Get inventory item for editing if ID is provided
        if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
            $editInventory = getById($pdo, 'Inventory', 'InventoryID', $_GET['edit']);
        }
        
        // Get all inventory items
        $inventoryItems = getAll($pdo, 'Inventory');
        
        // Get inventory summary
        $inventorySummary = $pdo->query("SELECT Type, SUM(Quantity) as TotalQuantity, SUM(Weight) as TotalWeight FROM Inventory GROUP BY Type")->fetchAll();
    } catch (PDOException $e) {
        error_log("Error retrieving inventory data: " . $e->getMessage());
    }
}

outputHeader("Inventory Management");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Inventory item successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (isset($_GET['error']) && $_GET['error'] == 'database'): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    A database error occurred. Please try again later.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Inventory Summary</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Total Quantity</th>
                                <th>Total Weight</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inventorySummary as $item): ?>
                            <tr>
                                <td><?= htmlspecialchars($item['Type']) ?></td>
                                <td><?= $item['TotalQuantity'] ?></td>
                                <td><?= $item['TotalWeight'] ? $item['TotalWeight'] . ' lbs' : 'N/A' ?></td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($inventorySummary)): ?>
                            <tr>
                                <td colspan="3" class="text-center">No inventory data found<?= !is_db_connected() ? ' (Database unavailable)' : '' ?></td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5><?= $editInventory ? 'Edit Inventory Item' : 'Add Inventory Item' ?></h5>
            </div>
            <div class="card-body">
                <?php if (!is_db_connected()): ?>
                <div class="alert alert-warning">
                    Inventory management is unavailable while the database is offline.
                </div>
                <?php else: ?>
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editInventory ? 'update' : 'add' ?>">
                    <?php if ($editInventory): ?>
                    <input type="hidden" name="inventory_id" value="<?= $editInventory['InventoryID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="date">Date:</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?= $editInventory['Date'] ?? date('Y-m-d') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Type:</label>
                        <select class="form-control" id="type" name="type" required>
                            <option value="">-- Select Type --</option>
                            <option value="Apples" <?= ($editInventory && $editInventory['Type'] == 'Apples') ? 'selected' : '' ?>>Apples</option>
                            <option value="Bags" <?= ($editInventory && $editInventory['Type'] == 'Bags') ? 'selected' : '' ?>>Bags</option>
                            <option value="Labels" <?= ($editInventory && $editInventory['Type'] == 'Labels') ? 'selected' : '' ?>>Labels</option>
                            <option value="Supplies" <?= ($editInventory && $editInventory['Type'] == 'Supplies') ? 'selected' : '' ?>>Supplies</option>
                            <option value="Other" <?= ($editInventory && $editInventory['Type'] == 'Other') ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="quantity">Quantity:</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" value="<?= $editInventory['Quantity'] ?? '' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="weight">Weight (lbs, for apples):</label>
                        <input type="number" step="0.01" class="form-control" id="weight" name="weight" value="<?= $editInventory['Weight'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="source">Source:</label>
                        <input type="text" class="form-control" id="source" name="source" value="<?= $editInventory['Source'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="cost">Cost ($):</label>
                        <input type="number" step="0.01" class="form-control" id="cost" name="cost" value="<?= $editInventory['Cost'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?= $editInventory['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-warning"><?= $editInventory ? 'Update' : 'Add' ?> Item</button>
                        <?php if ($editInventory): ?>
                        <a href="inventory.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Inventory Items</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Quantity</th>
                                <th>Source</th>
                                <th>Cost</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($inventoryItems as $item): ?>
                            <tr>
                                <td><?= $item['InventoryID'] ?></td>
                                <td><?= $item['Date'] ?></td>
                                <td><?= htmlspecialchars($item['Type']) ?></td>
                                <td><?= $item['Quantity'] ?> <?= $item['Weight'] ? '(' . $item['Weight'] . ' lbs)' : '' ?></td>
                                <td><?= htmlspecialchars($item['Source'] ?? 'N/A') ?></td>
                                <td><?= $item['Cost'] ? '$' . number_format($item['Cost'], 2) : 'N/A' ?></td>
                                <td>
                                    <?php if (is_db_connected()): ?>
                                    <div class="btn-group btn-group-sm">
                                        <a href="inventory.php?edit=<?= $item['InventoryID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this item?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="inventory_id" value="<?= $item['InventoryID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">Unavailable</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($inventoryItems)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No inventory items found<?= !is_db_connected() ? ' (Database unavailable)' : '' ?></td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>