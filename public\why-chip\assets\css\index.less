@import url('https://fonts.googleapis.com/css?family=Merriweather|Tajawal|Kadwa');

// Font Sizes
@font-wumbo: 76;
@font-xxl: 48;
@font-xl: 36;
@font-lp: 28;
@font-l: 24;
@font-sm: 20;
@font-mini: 18;
@font-default: 17px/1.5 'Tajawal', sans-serif;
@altFont: bold small-caps 20px/1.5 'Kadwa', serif;
@headerFont: 'Merriweather', serif;

// Colors
@text-color: #000;
@border-color: #333;
@nav-color: #005583;
@footer-color: #deb887;
@navlink-color: #fff;
@header-color: #f2f2f1;
@border2-color: #999;

// Gapping
@gapping: 1rem;

// Borders
@border-sm: 1px solid;
@border-m: 2px solid;
@border-l: 5px solid;

// Margins
.zero-margin {
  margin: 0;
}

.auto-hor-margin {
  margin: 0 auto;
}

// Dynamic Text
.dynamic_t(@min-font, @max-font) {
  font-size: calc(unit(@min-font, px) + (@max-font - @min-font) * (100vw - 320px) / (1200 - 320));
}
/************* General Styles **************/

* {
  box-sizing: border-box;
}

html {
  font: @font-default;
}

body {
  margin: ( @gapping * 4 ) 0 0;
}

h1,
h2,
h3 {
  font-family: @headerFont;
}

img {
  border: @border-color @border-sm;
  height: auto;
  max-width: 100%;
}

/******** Widths for anything needed to be centered on page **************/

#page-header,
nav ul,
#pool-columns,
#blurbs,
footer p {
  .auto-hor-margin;
  max-width: 1183px;
  width: 90%;
}

/******** Page Header *************/

#page-header {
  display: flex;
  flex-wrap: wrap;

  div:first-child {
    flex: 1;
    order: 2;
    text-align: center;
  }

  div:last-child {
    flex: 1;
    margin-right: ( @gapping / 2 );
    order: 1;
  }

  h1 {
    font-size: unit(@font-xl, px);
    .zero-margin;
  }

  p {
    font: @altFont;
    .zero-margin;
  }

  img {
    display: block;
    .auto-hor-margin;
  }
}

/************** Navbar ****************/

nav {
  background-color: @nav-color;
  border-top: @border-color @border-l;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;

  ul {
    padding: 0;
  }

  li {
    display: inline;
  }

  a {
    color: @navlink-color;
    display: inline-block;
    font-weight: bold;
    padding: ( @gapping / 2 );
    text-decoration: none;
    &:hover {
      background-color: @navlink-color;
      color: @border-color;
    }
  }
}

#nav-trigger {
  background-color: @text-color;
  color: @navlink-color;
  cursor: pointer;
  display: block;
  padding: ( @gapping / 2 );
  text-align: center;
}

#nav-trigger,
#nav-checkbox {
  display: none;
}

/************ Main Header **************/

main {
  header {
    background-color: @header-color;
    border-bottom: @border2-color @border-sm;
    border-top: @border2-color @border-sm;
    margin-top: @gapping;
    padding: @gapping;
    text-align: center;
    
    h2 {
      font-size: unit(@font-sm, px);
      .zero-margin;
    }

    p {
    font-size: unit(@font-mini, px);
    .zero-margin;
    }
  }
}

.phone {
  font-size: unit(@font-l, px);
  margin-bottom: 0;
}

/*********** Pool columns **************/

#pool-columns {
  display: flex;
  flex-wrap: wrap;
  padding: 0 @gapping;

  section {
    flex: 1;
    margin-right: @gapping;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

/************** Specials ************/

#special {
  background-color: @header-color;
  border-bottom: @border-color @border-sm;
  border-top: @border-color @border-sm;
  padding: @gapping;
  text-align: center;

  h2 {
    font-size: 5vw;
    .zero-margin;
  }

  p {
    margin-top: 0;
  }
}

/***************** Blurbs **************/

#blurbs {
  display: flex;
  flex-wrap: wrap;
  padding: 0 @gapping;
  
  section {
    flex: 1;
    margin-right: @gapping;
    text-align: center;

    &:last-child {
      margin-right: 0;
    }
  }

  img {
    border: @nav-color @border-l;
    border-radius: 50%;
  }
}

/************* Footer ****************/

footer {
  background-color: @footer-color;
  border-top: @border-color @border-m;

  p {
    padding: @gapping;
  }
}
 
/************** Fluid text starts at 320px *************/

@media screen and (min-width: 320px) {
  #page-header {
    h1 {
      .dynamic_t(@font-xl, @font-wumbo);
    }
  
    p {
      .dynamic_t(@font-sm, @font-xxl);
    }
  }  
  
  main header {
    h2 {
      .dynamic_t(@font-sm, @font-xxl);
    }
  
    p {
      .dynamic_t(@font-mini, @font-xl);
    }
  }
  
  .phone {
    .dynamic_t(@font-l, @font-xxl);
  }
}

/************ 794px and narrower ****************/

@media screen and (max-width: 794px) {
  
  
  nav {
    ul {
      display: none;
      width: 100%;
    }

    #nav-trigger,
    #nav-checkbox:checked + ul,
    li,
    a {
    display: block;
    }
    
    a {
      border-bottom: @text-color @border-m;
      min-height: 45px;
    }
  
    #pool-columns {
      display: block;

      img {
        float: left;
        margin: 0 ( @gapping * 2 ) @gapping 0;
      }

      p {
        clear: left;
        margin-bottom: @gapping * 2;
      }
    }  
  }
} 

/************** 569px and narrower ****************/

@media screen and (max-width: 569px) {
  #page-header {
    flex-direction: column-reverse;
  }
  
  #pool-columns img {
    float: none;
    .zero-margin;
  }
  
  #blurbs {
    display: block;

    p {
      padding-bottom: ( @gapping * 2 );
    }
  }
}

/************** Largest font thresholds *************/

@media screen and (min-width: 1200px) {
  #page-header {
    h1 {
      font-size: unit(@font-wumbo, px);
    }
  
    p {
      font-size: unit(@font-xxl, px);
    }
  }
  
  main header {
    h2 {
      font-size: unit(@font-xxl, px);
    }
  
    p {
      font-size: unit(@font-xl, px);
    }
  }

  .phone {
    font-size: unit(@font-xxl, px);
  }
}

