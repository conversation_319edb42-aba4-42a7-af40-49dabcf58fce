<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessible Technology Guide | April Cyr</title>
    <style>
        :root {
            --primary: #4a86e8;
            --secondary: #6aa84f;
            --light: #f8f8f8;
            --dark: #333;
            --gray: #767676;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background-color: #fff;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        header {
            background-color: var(--primary);
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .nav-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo a {
            color: white;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        nav ul {
            display: flex;
            list-style: none;
        }
        
        nav li {
            margin-left: 1.5rem;
        }
        
        nav a {
            color: white;
            text-decoration: none;
        }
        
        nav a:hover {
            text-decoration: underline;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }
        
        .hero p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            color: var(--gray);
        }
        
        .intro {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .topics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .topic-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .topic-card:hover {
            transform: translateY(-5px);
        }
        
        .topic-card h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .topic-card p {
            margin-bottom: 1rem;
            color: var(--dark);
        }
        
        .topic-card a {
            color: var(--secondary);
            font-weight: bold;
            text-decoration: none;
        }
        
        .topic-card a:hover {
            text-decoration: underline;
        }
        
        .help-section {
            background-color: var(--light);
            padding: 2rem;
            border-radius: 8px;
            margin: 3rem 0;
            text-align: center;
        }
        
        .help-section h2 {
            color: var(--primary);
            margin-bottom: 1rem;
        }
        
        .cta-button {
            display: inline-block;
            background: var(--primary);
            color: white;
            padding: 12px 24px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            margin: 1rem 0.5rem;
        }
        
        .cta-button.secondary {
            background: var(--secondary);
        }
        
        .articles-list {
            margin: 2rem 0;
        }
        
        .article-preview {
            border-bottom: 1px solid #eee;
            padding: 1.5rem 0;
        }
        
        .article-preview h3 {
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        
        .article-meta {
            color: var(--gray);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .article-preview p {
            margin-bottom: 1rem;
        }
        
        .read-more {
            color: var(--secondary);
            font-weight: bold;
            text-decoration: none;
        }
        
        footer {
            text-align: center;
            margin-top: 3rem;
            padding: 2rem 0;
            color: var(--gray);
            border-top: 1px solid #eee;
        }
        
        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
            }
            
            nav ul {
                margin-top: 1rem;
            }
            
            .topics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="nav-container">
            <div class="logo">
                <a href="index.html">April Cyr</a>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="portfolio.html">Portfolio</a></li>
                    <li><a href="blog.html">Blog</a></li>
                    <li><a href="hire.html">Hire Me</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="hero">
            <h1>Making Technology Work for You</h1>
            <p>A friendly guide to accessible technology tools and practices that make computers easier to use</p>
        </div>

        <div class="intro">
            <h2>Technology Should Empower, Not Intimidate</h2>
            <p>If you've ever felt confused, frustrated, or overwhelmed by technology, you're not alone. Many people struggle with computers, phones, and apps that seem designed for experts rather than everyday users.</p>
            <p>The good news is that there are tools and approaches that can make technology work for you—on your terms. In this resource hub, you'll find guides, tips, and explanations designed specifically for people who want to use technology without the stress.</p>
        </div>

        <h2>Explore Accessible Technology Topics</h2>
        <div class="topics-grid">
            <div class="topic-card">
                <h3>Built-In Accessibility Features</h3>
                <p>Discover the helpful tools already built into your computer, phone, or tablet that can make them easier to see, hear, and use.</p>
                <a href="#">View Articles →</a>
            </div>
            
            <div class="topic-card">
                <h3>Understanding Websites</h3>
                <p>Learn what websites are, how they work, and how you can create your own simple website to share your ideas with the world.</p>
                <a href="#">View Articles →</a>
            </div>
            
            <div class="topic-card">
                <h3>Organize Your Thoughts with Obsidian</h3>
                <p>Discover a free, powerful tool for note-taking and organizing your ideas without getting overwhelmed by complexity.</p>
                <a href="#">View Articles →</a>
            </div>
            
            <div class="topic-card">
                <h3>Why Digital Accessibility Matters</h3>
                <p>Learn how thoughtful technology design helps everyone participate fully in our digital world.</p>
                <a href="#">View Articles →</a>
            </div>
        </div>

        <div class="help-section">
            <h2>Need Personalized Help?</h2>
            <p>Sometimes, having a patient guide makes all the difference. I offer one-on-one tech support sessions tailored to your specific needs and learning style.</p>
            <p>Whether you want to set up a website, learn to use accessibility features, or organize your digital life, I'm here to help.</p>
            <a href="hire.html" class="cta-button">Book a Consultation</a>
            <a href="contact.html" class="cta-button secondary">Ask a Question</a>
        </div>

        <div class="articles-list">
            <h2>Latest Articles on Accessible Technology</h2>
            
            <div class="article-preview">
                <h3>5 Built-in Computer Features That Make Life Easier</h3>
                <div class="article-meta">Posted on April 12, 2023 • 5 min read</div>
                <p>Your computer already has tools to help you see, hear, and navigate more easily. Learn how to find and use them.</p>
                <a href="#" class="read-more">Read Article →</a>
            </div>
            
            <div class="article-preview">
                <h3>What Exactly Is a Website? A Beginner's Explanation</h3>
                <div class="article-meta">Posted on March 28, 2023 • 7 min read</div>
                <p>Websites might seem mysterious, but they're just digital spaces for sharing information. Learn how they work in simple terms.</p>
                <a href="#" class="read-more">Read Article →</a>
            </div>
            
            <div class="article-preview">
                <h3>Getting Started with Obsidian: Your Digital Notebook</h3>
                <div class="article-meta">Posted on March 15, 2023 • 8 min read</div>
                <p>Obsidian is a free tool that helps you organize your thoughts without forcing you into a complicated system.</p>
                <a href="#" class="read-more">Read Article →</a>
            </div>
            
            <div class="article-preview">
                <h3>Why Accessibility Benefits Everyone</h3>
                <div class="article-meta">Posted on February 22, 2023 • 6 min read</div>
                <p>When we design technology to include people with disabilities, we often make it better for all users.</p>
                <a href="#" class="read-more">Read Article →</a>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>© 2023 April Cyr | Accessibility-focused Tech Support</p>
            <p>Have a question or suggestion? <a href="contact.html">Get in touch</a>!</p>
        </div>
    </footer>
</body>
</html>