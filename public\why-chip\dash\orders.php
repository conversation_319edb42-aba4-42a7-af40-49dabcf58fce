<?php
// orders.php - Order Management
require_once 'config/database.php';
require_once 'includes/functions.php';

// Process form submission for adding/updating orders
if ($_SERVER['REQUEST_METHOD'] == 'POST' && is_db_connected()) {
    if (isset($_POST['action'])) {
        try {
            if ($_POST['action'] == 'add') {
                // Add new order
                $stmt = $pdo->prepare("INSERT INTO Orders (CustomerID, OrderDate, DeliveryDate, Quantity, Amount, PaymentStatus, PaymentMethod, Notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $_POST['customer_id'] ?: null, 
                    $_POST['order_date'], 
                    $_POST['delivery_date'] ?: null, 
                    $_POST['quantity'], 
                    $_POST['amount'], 
                    $_POST['payment_status'], 
                    $_POST['payment_method'], 
                    $_POST['notes']
                ]);
                header("Location: orders.php?success=added");
                exit;
            } elseif ($_POST['action'] == 'update') {
                // Update existing order
                $stmt = $pdo->prepare("UPDATE Orders SET CustomerID = ?, OrderDate = ?, DeliveryDate = ?, Quantity = ?, Amount = ?, PaymentStatus = ?, PaymentMethod = ?, Notes = ? WHERE OrderID = ?");
                $stmt->execute([
                    $_POST['customer_id'] ?: null, 
                    $_POST['order_date'], 
                    $_POST['delivery_date'] ?: null, 
                    $_POST['quantity'], 
                    $_POST['amount'], 
                    $_POST['payment_status'], 
                    $_POST['payment_method'], 
                    $_POST['notes'],
                    $_POST['order_id']
                ]);
                header("Location: orders.php?success=updated");
                exit;
            } elseif ($_POST['action'] == 'delete') {
                // Delete order
                $stmt = $pdo->prepare("DELETE FROM Orders WHERE OrderID = ?");
                $stmt->execute([$_POST['order_id']]);
                header("Location: orders.php?success=deleted");
                exit;
            }
        } catch (PDOException $e) {
            error_log("Error in order form processing: " . $e->getMessage());
            header("Location: orders.php?error=database");
            exit;
        }
    }
}

// Initialize variables
$editOrder = null;
$orders = [];
$customers = [];

if (is_db_connected()) {
    try {
        // Get order for editing if ID is provided
        if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
            $stmt = $pdo->prepare("SELECT * FROM Orders WHERE OrderID = ?");
            $stmt->execute([$_GET['edit']]);
            $editOrder = $stmt->fetch();
        }
        
        // Get all orders with customer names
        $stmt = $pdo->prepare("SELECT o.*, c.Name AS CustomerName FROM Orders o LEFT JOIN Customers c ON o.CustomerID = c.CustomerID ORDER BY o.OrderDate DESC");
        $stmt->execute();
        $orders = $stmt->fetchAll();
        
        // Get all customers for dropdown
        $stmt = $pdo->prepare("SELECT CustomerID, Name FROM Customers ORDER BY Name");
        $stmt->execute();
        $customers = $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error retrieving order data: " . $e->getMessage());
    }
}

outputHeader("Order Management");
?>

<?php if (isset($_GET['success'])): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    Order successfully <?= $_GET['success'] ?>.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if (isset($_GET['error']) && $_GET['error'] == 'database'): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    A database error occurred. Please try again later.
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><?= $editOrder ? 'Edit Order' : 'Add New Order' ?></h5>
            </div>
            <div class="card-body">
                <?php if (!is_db_connected()): ?>
                <div class="alert alert-warning">
                    Order management is unavailable while the database is offline.
                </div>
                <?php else: ?>
                <form method="post">
                    <input type="hidden" name="action" value="<?= $editOrder ? 'update' : 'add' ?>">
                    <?php if ($editOrder): ?>
                    <input type="hidden" name="order_id" value="<?= $editOrder['OrderID'] ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="customer_id">Customer:</label>
                        <select class="form-control" id="customer_id" name="customer_id">
                            <option value="">-- Select Customer --</option>
                            <?php foreach ($customers as $customer): ?>
                            <option value="<?= $customer['CustomerID'] ?>" <?= ($editOrder && $editOrder['CustomerID'] == $customer['CustomerID']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($customer['Name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="order_date">Order Date:</label>
                        <input type="date" class="form-control" id="order_date" name="order_date" value="<?= $editOrder['OrderDate'] ?? date('Y-m-d') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="delivery_date">Delivery Date:</label>
                        <input type="date" class="form-control" id="delivery_date" name="delivery_date" value="<?= $editOrder['DeliveryDate'] ?? '' ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="quantity">Quantity (Bags):</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" value="<?= $editOrder['Quantity'] ?? '1' ?>" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="amount">Amount ($):</label>
                        <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="<?= $editOrder['Amount'] ?? '8.00' ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment_status">Payment Status:</label>
                        <select class="form-control" id="payment_status" name="payment_status" required>
                            <option value="Pending" <?= ($editOrder && $editOrder['PaymentStatus'] == 'Pending') ? 'selected' : '' ?>>Pending</option>
                            <option value="Paid" <?= ($editOrder && $editOrder['PaymentStatus'] == 'Paid') ? 'selected' : '' ?>>Paid</option>
                            <option value="Cancelled" <?= ($editOrder && $editOrder['PaymentStatus'] == 'Cancelled') ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment_method">Payment Method:</label>
                        <select class="form-control" id="payment_method" name="payment_method">
                            <option value="">-- Select Method --</option>
                            <option value="Cash" <?= ($editOrder && $editOrder['PaymentMethod'] == 'Cash') ? 'selected' : '' ?>>Cash</option>
                            <option value="PayPal" <?= ($editOrder && $editOrder['PaymentMethod'] == 'PayPal') ? 'selected' : '' ?>>PayPal</option>
                            <option value="Check" <?= ($editOrder && $editOrder['PaymentMethod'] == 'Check') ? 'selected' : '' ?>>Check</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes:</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"><?= $editOrder['Notes'] ?? '' ?></textarea>
                    </div>
                    
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-success"><?= $editOrder ? 'Update' : 'Add' ?> Order</button>
                        <?php if ($editOrder): ?>
                        <a href="orders.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-light">
                <h5>Order List</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Qty</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><?= $order['OrderID'] ?></td>
                                <td><?= $order['OrderDate'] ?></td>
                                <td><?= htmlspecialchars($order['CustomerName'] ?? 'N/A') ?></td>
                                <td><?= $order['Quantity'] ?></td>
                                <td>$<?= number_format($order['Amount'], 2) ?></td>
                                <td><span class="badge bg-<?= $order['PaymentStatus'] == 'Paid' ? 'success' : ($order['PaymentStatus'] == 'Pending' ? 'warning' : 'danger') ?>">
                                    <?= $order['PaymentStatus'] ?>
                                </span></td>
                                <td>
                                    <?php if (is_db_connected()): ?>
                                    <div class="btn-group btn-group-sm">
                                        <a href="orders.php?edit=<?= $order['OrderID'] ?>" class="btn btn-warning">Edit</a>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this order?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="order_id" value="<?= $order['OrderID'] ?>">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">Unavailable</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($orders)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No orders found<?= !is_db_connected() ? ' (Database unavailable)' : '' ?></td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php outputFooter(); ?>