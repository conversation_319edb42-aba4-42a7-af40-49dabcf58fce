<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Friendly Tech Help - April Cyr</title>
  <style>

        /* Resume-specific styling */
    .resume-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    .resume-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .resume-section {
      margin-bottom: 2rem;
    }
    
    .skills-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
    }
    
    .skill-category {
      background: #f8f8f8;
      padding: 1rem;
      border-radius: 5px;
    }
    
    .experience-item, .education-item {
      margin-bottom: 1.5rem;
    }
    
    .contact-options {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 2rem;
    }
    
    .appointment-form {
      background: #f8f8f8;
      padding: 2rem;
      border-radius: 5px;
      margin-top: 2rem;
    }

    @media print {
      header, footer, .contact-options, .appointment-form {
        display: none;
      }
      
      .resume-container {
        padding: 0;
      }
    }
    
    /* Your existing styles would be enhanced with these additions */
    .service-card {
      background: #f8f8f8;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
    }
    .testimonial {
      font-style: italic;
      border-left: 3px solid #ccc;
      padding-left: 1rem;
      margin: 1.5rem 0;
    }
    .cta-button {
      display: inline-block;
      background: #4a86e8;
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
      margin: 0.5rem;
    }
    .cta-button.secondary {
      background: #6aa84f;
    }
  </style>
</head>
<body>
  <!-- Your existing header would remain -->

  <div class="container">
    <main>
      <div class="resume-container">
        <!-- New Hero Section -->
        <div class="resume-header">
          <h1>Tech Support That Actually Makes Sense</h1>
          <p>Hi, I'm April. I help everyday people use technology without the confusion and frustration.</p>
        </div>
        
        <!-- Problem Statement -->
        <div class="resume-section">
          <h2>Does technology sometimes feel like it's working against you?</h2>
          <p>You're not alone. Many people struggle with:</p>
          <ul>
            <li>Setting up websites that actually represent you or your business</li>
            <li>Organizing your digital notes and information</li>
            <li>Learning new apps and software without getting overwhelmed</li>
            <li>Making technology accessible for your specific needs</li>
          </ul>
          <p>I provide patient, personalized help that focuses on what <strong>you</strong> need to accomplish.</p>
        </div>
        
        <!-- Services Section -->
        <div class="resume-section">
          <h2>How I Can Help You</h2>
          
          <div class="service-card">
            <h3>🌐 Website Help</h3>
            <p>Whether you need a simple website, help with an existing site, or want to learn how to manage your own content, I can help you create an online presence that works for you.</p>
            <p><strong>Perfect for:</strong> Small business owners, artists, freelancers, and organizations who need a website but don't know where to start.</p>
          </div>
          
          <div class="service-card">
            <h3>📝 Note Management & Organization</h3>
            <p>Specialized help with Obsidian—a powerful but approachable tool for organizing your thoughts, projects, and information. I'll help you set up a system that makes sense for how you think and work.</p>
            <p><strong>Perfect for:</strong> Writers, students, researchers, and anyone who feels overwhelmed by information overload.</p>
          </div>
          
          <div class="service-card">
            <h3>💻 Accessible Technology Coaching</h3>
            <p>Patient, one-on-one guidance to help you use technology more effectively in your daily life. I meet you where you are and help you build confidence with the tools you need to use.</p>
            <p><strong>Perfect for:</strong> Anyone who feels frustrated with technology or wants to use it more effectively for personal or professional goals.</p>
          </div>
        </div>
        
        <!-- Why Work With Me -->
        <div class="resume-section">
          <h2>Why People Enjoy Working With Me</h2>
          <ul>
            <li>I explain things clearly without technical jargon</li>
            <li>I focus on your goals, not just the technical details</li>
            <li>I'm patient and responsive to your learning style</li>
            <li>I have experience with diverse needs and abilities</li>
          </ul>
          
          <div class="testimonial">
            "April helped me set up a website for my small pottery business when I had no idea where to start. They were patient, explained things in ways I could understand, and now I can actually update my site myself!"
            <div>- Maria, small business owner</div>
          </div>
        </div>
        
        <!-- Multiple Call-to-Action Options -->
        <div class="resume-section">
          <h2>Ready to Make Technology Work for You?</h2>
          <p>Choose the option that feels most comfortable to you:</p>
          
          <div style="text-align: center; margin: 2rem 0;">
            <a href="#appointment-form" class="cta-button">Book a Free 20-Minute Consultation</a>
            <a href="mailto:<EMAIL>" class="cta-button secondary">Email Me Your Questions</a>
          </div>
          
          <p style="text-align: center;">Prefer to learn more first? <a href="#">Download my free guide to getting started with Obsidian</a> or <a href="#">view examples of websites I've helped create</a>.</p>
        </div>
        
        <!-- Your existing appointment form would remain here -->
        <div id="appointment-form" class="appointment-form">
          <!-- Form content -->
        </div>
      </div>
    </main>
  </div>
</body>
</html>